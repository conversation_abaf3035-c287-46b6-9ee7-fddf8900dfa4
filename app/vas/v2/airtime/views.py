# from ..auth.decorators import business_is_authenticated
from common.decorators import (
    merchant_onboarding_required,
    require_activated_vas_product,
)
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from transaction.enums import TransactionClassEnum

from .serializers import AirtimePurchaseRequestSerializer


@extend_schema_view(
    post=extend_schema(
        summary="Purchase Airtime",
        description="Endpoint to purchase airtime for a given phone number and network.",
        tags=["vas-airtime"],
    )
)
@require_activated_vas_product(TransactionClassEnum.AIRTIME.value)
class AirtimePurchaseView(generics.GenericAPIView):
    serializer_class = AirtimePurchaseRequestSerializer
    # authentication_classes = [BusinessJWTAuthentication]
    permission_classes = [permissions.IsAuthenticated]

    # @business_is_authenticated
    @merchant_onboarding_required
    def post(self, request, *args, **kwargs):
        business = request.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        data = serializer.save()
        return Response(data, status=status.HTTP_200_OK)
