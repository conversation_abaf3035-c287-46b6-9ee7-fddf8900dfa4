import logging
import os
from decimal import Decimal

import pyotp
from business.enums import (
    BusinessSection,
    BusinessStatusUpdateAction,
    ChangeRequestStatus,
    ChangeRequestType,
    DocumentName,
    DocumentStatus,
    SocialMediaChannel,
)
from business.handlers.api_config_handler import APIConfigHandler
from business.handlers.onboarding_workflow import OnboardingWorkflowHandler
from business.handlers.social_media import SocialMediaHandler
from business.models import (
    APIConfig,
    Business,
    BusinessChangeRequest,
    Director,
    Document,
    SettlementDetail,
    SocialMedia,
)
from business.utils import (
    BUSINESS_INFO_FIELD_LABELS,
    DIRECTOR_FIELD_LABELS,
    DOCUMENTATION_FIELD_LABELS,
    SETTLEMENT_FIELD_LABELS,
)
from django.db import transaction
from email_validator import EmailNotValidError, validate_email
from pykolofinance.common.helpers import clean_phone_number
from rest_framework import serializers
from user.enums import Auth2FATypeEnums
from user.models import User

logger = logging.getLogger(__name__)


class BusinessInformationSerializer(serializers.ModelSerializer):
    owner_name = serializers.CharField(source="owner.fullname", read_only=True)
    owner_email = serializers.CharField(source="owner.email", read_only=True)

    class Meta:
        model = Business
        fields = (
            "name",
            "email",
            "description",
            "phone",
            "rc_number",
            "street",
            "city",
            "state",
            "office_address",
            "website",
            "postal_code",
            "state",
            "owner_name",
            "owner_email",
        )
        read_only_fields = (
            "owner_name",
            "owner_email",
        )

    def validate(self, attrs):
        data = super().validate(attrs)
        data["phone"] = clean_phone_number(attrs.get("phone").strip())

        if (
            Business.objects.filter(phone=data["phone"])
            .exclude(pk=self.instance.pk if self.instance else None)
            .exists()
        ):
            raise serializers.ValidationError({"phone": "Phone number already exists"})

        if (
            Business.objects.filter(email=data["email"])
            .exclude(pk=self.instance.pk if self.instance else None)
            .exists()
        ):
            raise serializers.ValidationError({"email": "Email already exists"})

        try:
            validated_email = validate_email(data["email"])
            data["email"] = validated_email.normalized
        except EmailNotValidError as e:
            raise serializers.ValidationError({"email": str(e)})

        return data

    def update(self, instance, validated_data):
        instance: Business = instance
        instance.email = validated_data.get("email")
        instance.description = validated_data.get("description")
        instance.phone = validated_data.get("phone")
        instance.rc_number = validated_data.get("rc_number")
        instance.street = validated_data.get("street")
        instance.city = validated_data.get("city")
        instance.state = validated_data.get("state")
        instance.office_address = validated_data.get("office_address")
        instance.postal_code = validated_data.get("postal_code")
        instance.website = validated_data.get("website")
        instance.save()
        OnboardingWorkflowHandler(instance).advance_from_business_information()

        return instance


class BusinessListSerializer(serializers.ModelSerializer):
    owner_name = serializers.CharField(source="owner.fullname", read_only=True)
    owner_email = serializers.CharField(source="owner.email", read_only=True)
    business_name = serializers.CharField(source="name", read_only=True)
    business_email = serializers.CharField(source="email", read_only=True)
    general_wallet_balance = serializers.SerializerMethodField()

    class Meta:
        model = Business
        fields = (
            "id",
            "business_name",
            "business_email",
            "status",
            "owner_name",
            "owner_email",
            "general_wallet_balance",
            "rc_number",
            "created_at",
        )

    def get_general_wallet_balance(self, obj):
        # we return the pre-calculated balance from the view
        balance = getattr(obj, "general_wallet_balance", Decimal("0.00"))
        return str(balance)


class BusinessStatusUpdateSerializer(serializers.Serializer):
    """
    Serializer to handle status updates for a Business.
    It expects an 'action' and acts on a provided Business instance.
    """

    action = serializers.ChoiceField(
        choices=BusinessStatusUpdateAction.choices(),
        write_only=True,
        help_text="The action to perform on the business status (ACTIVATE, DEACTIVATE, VERIFY).",
    )
    current_status = serializers.CharField(source="status", read_only=True)
    message = serializers.CharField(read_only=True)

    def update(self, instance: Business, validated_data: dict):
        """
        Performs the status update on the provided Business instance.
        """
        action_type = validated_data["action"]
        status_changed = False
        message = ""

        match action_type:
            case BusinessStatusUpdateAction.ACTIVATE.value:
                status_changed = instance.activate()
                message = "Business successfully activated."
            case BusinessStatusUpdateAction.DEACTIVATE.value:
                status_changed = instance.deactivate()
                message = "Business successfully deactivated."
            case BusinessStatusUpdateAction.VERIFY.value:
                status_changed = instance.verify()
                message = "Business successfully verified."
            case _:
                raise serializers.ValidationError("Invalid action provided.")

        instance._message_for_response = message
        instance._status_changed_for_response = status_changed
        return instance

    def to_representation(self, instance):
        data = {
            "current_status": instance.status,
        }
        if (
            hasattr(instance, "_status_changed_for_response")
            and instance._status_changed_for_response
        ):
            data["message"] = getattr(
                instance, "_message_for_response", "Status updated successfully."
            )
        else:
            data["message"] = f"Business status is already {instance.status.lower()}."
        return data


class UploadDocumentSerializer(serializers.ModelSerializer):
    document_name = serializers.ChoiceField(
        required=True, choices=DocumentName.for_upload_documents_screen()
    )
    document = serializers.FileField(required=True)

    class Meta:
        model = Document
        fields = (
            "document",
            "document_name",
        )

    def create(self, validated_data):
        user: User = self.context["user"]
        business: Business = user.business

        with transaction.atomic():
            Document.objects.update_or_create(
                business=business,
                document_name=validated_data["document_name"],
                defaults={
                    "document": validated_data["document"],
                    "status": DocumentStatus.Pending.value,
                },
            )

            OnboardingWorkflowHandler(business).advance_from_documentation()

        return business


class UploadDocumentChangeRequestSerializer(serializers.ModelSerializer):
    document_name = serializers.ChoiceField(
        required=True, choices=DocumentName.for_upload_documents_screen()
    )
    document = serializers.FileField(required=True)

    class Meta:
        model = Document
        fields = (
            "document",
            "document_name",
        )

    def validate(self, attrs):
        user: User = self.context["user"]
        business = user.business
        document_name_value = attrs["document_name"]
        new_file = attrs["document"]

        latest_existing_doc = (
            Document.objects.filter(
                business=business,
                document_name=document_name_value,
            )
            .exclude(status=DocumentStatus.ChangeRequested.value)
            .order_by("-created_at")
            .first()
        )

        if not latest_existing_doc:
            raise serializers.ValidationError(
                {
                    "document": f"No existing '{document_name_value}' document found to update."
                }
            )
        # For robust duplicate file detection
        # We will compare file hashes (MD5/SHA256) for better accuracy.
        existing_filename = os.path.basename(latest_existing_doc.document.name)
        new_filename = new_file.name
        if (
            existing_filename == new_filename
            and new_file.size == latest_existing_doc.document.size
        ):
            raise serializers.ValidationError(
                "No changes detected in the document file."
            )

        pending_change_request_exists = Document.objects.filter(
            business=business,
            document_name=document_name_value,
            status=DocumentStatus.ChangeRequested.value,
            parent_document=latest_existing_doc,
        ).exists()

        if pending_change_request_exists:
            raise serializers.ValidationError(
                f"A pending change request for '{document_name_value}' is already awaiting review."
                " Please wait for it to be reviewed."
            )

        attrs["__parent_document__"] = latest_existing_doc
        attrs["__old_doc_url__"] = latest_existing_doc.document.url

        attrs["__section__"] = (
            f"{BusinessSection.Documentation.value}_{document_name_value.replace(' ', '_').title()}"
        )

        return attrs

    def create(self, validated_data):
        user: User = self.context["user"]
        business = user.business

        parent_document = validated_data.pop("__parent_document__")
        old_doc_url = validated_data.pop("__old_doc_url__")
        section = validated_data.pop("__section__")

        uploaded_file = validated_data["document"]
        document_name = validated_data["document_name"]

        new_doc = Document.objects.create(
            business=business,
            document=uploaded_file,
            document_name=document_name,
            status=DocumentStatus.ChangeRequested.value,
            parent_document=parent_document,
        )
        logger.info(
            f"Created new Document (ID: {new_doc.id}, Name: {new_doc.document_name}) "
            f"as a change request for parent Document ID: {parent_document.id}"
        )

        old_value = {"url": old_doc_url}
        new_value = {"url": new_doc.document.url, "id": new_doc.id}

        object_id = str(new_doc.pk)

        with transaction.atomic():
            business_change_request = BusinessChangeRequest.objects.create(
                business=business,
                section=section,
                old_value=old_value,
                new_value=new_value,
                object_id=object_id,
                status=ChangeRequestStatus.Pending.value,
                created_by=user,
            )
            logger.info(
                f"Created BusinessChangeRequest (ID: {business_change_request.id}) "
                f"for Document ID: {new_doc.id}"
            )

        return business_change_request


class DocumentMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Document
        fields = (
            "document",
            "document_name",
            "rejection_message",
            "status",
            "created_at",
        )


class ListSocialMediaSerializer(serializers.ListSerializer):
    def create(self, validated_data):
        results = []
        for item in validated_data:
            serializer = self.child
            instance = serializer.create(item)
            results.append(instance)
        return results


class SocialMediaSerializer(serializers.ModelSerializer):
    channel = serializers.ChoiceField(choices=SocialMediaChannel.choices())

    class Meta:
        model = SocialMedia
        fields = (
            "id",
            "channel",
            "url",
        )
        read_only_fields = (
            "id",
            "business",
        )

    def create(self, validated_data):
        user: User = self.context["user"]
        business: Business = user.business
        validated_data["business"] = business

        SocialMediaHandler().add(business, validated_data)

        return validated_data


class MultipleSocialMediaSerializer(serializers.Serializer):
    social_medias = SocialMediaSerializer(many=True)

    def validate(self, attrs):
        data = super().validate(attrs)
        if not data["social_medias"]:
            raise serializers.ValidationError(
                "At least one social media item is required."
            )

        return data

    def create(self, validated_data):
        user: User = self.context["user"]
        business: Business = user.business
        data = validated_data["social_medias"]

        for _data in data:
            SocialMediaHandler().add(business, _data)

        return data


class AddDirectorSerializer(serializers.ModelSerializer):
    nin_file = serializers.FileField(required=True)

    class Meta:
        model = Director
        fields = ("name", "email", "phone", "bvn", "nin_file")
        read_only_fields = ("business",)

    def validate(self, attrs):
        data = super().validate(attrs)
        data["phone"] = clean_phone_number(attrs.get("phone").strip())

        # TODO: Add bvn validator via VAS Gate

        user: User = self.context["user"]
        business: Business = user.business

        if Director.objects.filter(phone=data["phone"], business=business).exists():
            raise serializers.ValidationError({"phone": "Phone number already exists"})

        if Director.objects.filter(email=data["email"], business=business).exists():
            raise serializers.ValidationError({"email": "Email already exists"})

        if Director.objects.filter(bvn=data["bvn"], business=business).exists():
            raise serializers.ValidationError({"email": "Bvn already exists"})

        try:
            validated_email = validate_email(data["email"])
            data["email"] = validated_email.normalized
        except EmailNotValidError as e:
            raise serializers.ValidationError({"email": str(e)})

        return data

    def create(self, validated_data):
        user: User = self.context["user"]
        business: Business = user.business
        nin_file = validated_data.pop("nin_file")
        validated_data["business"] = business

        with transaction.atomic():
            document, _ = Document.objects.update_or_create(
                business=business,
                document_name=DocumentName.DirectorNin.value,
                defaults={
                    "document": nin_file,
                    "status": DocumentStatus.Pending.value,
                },
            )
            Director.objects.create(**validated_data, nin_document=document)

            OnboardingWorkflowHandler(business).advance_from_directors()

        return business


class AddUpdateDirectorChangeRequestSerializer(serializers.Serializer):
    director_id = serializers.CharField(required=False)
    name = serializers.CharField(required=False)
    email = serializers.EmailField(required=False)
    phone = serializers.CharField(required=False)
    bvn = serializers.CharField(required=False)
    nin_file = serializers.FileField(required=False)

    def validate(self, attrs):
        self.business: Business = self.context["business"]
        self.user = self.business.owner
        self.diffs = {}

        if not attrs.get("director_id"):
            return self._validate_new_director(attrs)

        return self._validate_existing_director(attrs)

    def _validate_new_director(self, attrs):
        required_fields = ["name", "email", "phone", "bvn", "nin_file"]
        missing = [field for field in required_fields if not attrs.get(field)]
        if missing:
            raise serializers.ValidationError(
                {
                    field: "This field is required when creating a new director"
                    for field in missing
                }
            )

        self._normalize_and_check_uniqueness(attrs)
        self._check_pending_new_director_conflicts(attrs)

        self.diffs = {
            "name": {"old": None, "new": attrs["name"]},
            "email": {"old": None, "new": attrs["email"]},
            "phone": {"old": None, "new": attrs["phone"]},
            "bvn": {"old": None, "new": attrs["bvn"]},
            "nin_document": {"old": None, "new": attrs["nin_file"].name},
        }

        attrs["director"] = None
        attrs["diffs"] = self.diffs
        return attrs

    def _validate_existing_director(self, attrs):
        director_id = attrs["director_id"]
        try:
            director = Director.objects.get(pk=director_id, business=self.business)
        except Director.DoesNotExist:
            raise serializers.ValidationError("Director not found")

        attrs["director"] = director

        if "name" in attrs and attrs["name"] != director.name:
            self.diffs["name"] = {"old": director.name, "new": attrs["name"]}

        if "email" in attrs:
            try:
                validated_email = validate_email(attrs["email"])
                normalized = validated_email.normalized
                if normalized != director.email:
                    self.diffs["email"] = {"old": director.email, "new": normalized}
                    attrs["email"] = normalized
            except EmailNotValidError as e:
                raise serializers.ValidationError({"email": str(e)})

        if "phone" in attrs:
            cleaned = clean_phone_number(attrs["phone"].strip())
            if cleaned != director.phone:
                self.diffs["phone"] = {"old": director.phone, "new": cleaned}
                attrs["phone"] = cleaned

        if "bvn" in attrs and attrs["bvn"].strip() != director.bvn:
            # TODO: Add bvn validator via VAS Gate
            self.diffs["bvn"] = {"old": director.bvn, "new": attrs["bvn"].strip()}
            attrs["bvn"] = attrs["bvn"].strip()

        if "nin_file" in attrs:
            new_file = attrs["nin_file"]
            current_file = (
                director.nin_document.document.name if director.nin_document else None
            )
            if new_file.name != current_file:
                self.diffs["nin_document"] = {
                    "old": (
                        director.nin_document.document.url
                        if director.nin_document
                        else None
                    ),
                    "new": new_file.name,
                }

        if not self.diffs:
            raise serializers.ValidationError("No changes detected")

        self._check_pending_request(director.pk)
        attrs["diffs"] = self.diffs
        attrs["existing_nin_document"] = director.nin_document
        return attrs

    def _normalize_and_check_uniqueness(self, attrs):
        attrs["phone"] = clean_phone_number(attrs["phone"].strip())
        attrs["bvn"] = attrs["bvn"].strip()
        # TODO: Add bvn validator via VAS Gate

        try:
            validated_email = validate_email(attrs["email"])
            attrs["email"] = validated_email.normalized
        except EmailNotValidError as e:
            raise serializers.ValidationError({"email": str(e)})

        if Director.objects.filter(
            email=attrs["email"], business=self.business
        ).exists():
            raise serializers.ValidationError({"email": "Email already exists"})

        if Director.objects.filter(
            phone=attrs["phone"], business=self.business
        ).exists():
            raise serializers.ValidationError({"phone": "Phone number already exists"})

        if Director.objects.filter(bvn=attrs["bvn"], business=self.business).exists():
            raise serializers.ValidationError({"bvn": "BVN already exists"})

    def _check_pending_request(self, director_pk):
        exists = BusinessChangeRequest.objects.filter(
            business=self.business,
            section=BusinessSection.DirectorsAndOwners.value,
            object_id=str(director_pk),
            status=ChangeRequestStatus.Pending.value,
        ).exists()
        if exists:
            raise serializers.ValidationError(
                "A pending change request already exists for this director"
            )

    def _check_pending_new_director_conflicts(self, attrs):
        query_fields = ["email", "phone", "bvn"]
        section = BusinessSection.DirectorsAndOwners.value

        for field in query_fields:
            value = attrs[field]
            exists = BusinessChangeRequest.objects.filter(
                business=self.business,
                section=section,
                status=ChangeRequestStatus.Pending.value,
                new_value__icontains=value,
            ).exists()

            if exists:
                raise serializers.ValidationError(
                    {
                        field: f"A pending change request already exists with this {field}"
                    }
                )

    def create(self, validated_data):
        director = validated_data.get("director")
        diffs = validated_data["diffs"]
        nin_file = validated_data.get("nin_file")
        existing_nin_document = validated_data.get("existing_nin_document", None)

        document = None
        if "nin_document" in diffs and nin_file:
            document = Document.objects.create(
                document=nin_file,
                document_name=DocumentName.DirectorNin.value,
                status=DocumentStatus.ChangeRequested.value,
                parent_document=existing_nin_document,
                business=self.business,
            )
            diffs["nin_document"]["new"] = {
                "url": document.document.url,
                "id": document.id,
            }

        object_id = str(director.pk) if director else None

        return BusinessChangeRequest.objects.create(
            business=self.business,
            section=BusinessSection.DirectorsAndOwners.value,
            change_type=(
                ChangeRequestType.Modification.value
                if object_id
                else ChangeRequestType.Creation.value
            ),
            object_id=object_id,
            old_value={k: v["old"] for k, v in diffs.items()},
            new_value={k: v["new"] for k, v in diffs.items()},
            status=ChangeRequestStatus.Pending.value,
            created_by=self.user,
        )


class DirectorListSerializer(serializers.ModelSerializer):
    """Serializer for listing directors"""

    nin = serializers.SerializerMethodField()

    class Meta:
        model = Director
        fields = (
            "id",
            "name",
            "email",
            "phone",
            "bvn",
            "nin",
        )
        read_only_fields = fields

    def get_nin(self, obj):
        return obj.nin_document.document.url if obj.nin_document else None


class SettlementDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SettlementDetail
        fields = (
            "bank_name",
            "bank_code",
            "account_number",
            "account_name",
            "is_active",
        )


class AddSettlementDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SettlementDetail
        fields = (
            "bank_code",
            "account_number",
        )

    def validate(self, attrs):

        verify_serializer = VerifyAccountNumberSerializer(data=attrs)
        verify_serializer.is_valid(raise_exception=True)

        verified_data = verify_serializer.validated_data

        attrs["account_name"] = verified_data.get("account_name")
        attrs["bank_name"] = verified_data.get("bank_name")

        return attrs

    def create(self, validated_data):
        user: User = self.context["user"]
        business: Business = user.business

        with transaction.atomic():
            settlement = (
                SettlementDetail.objects.filter(business=business)
                .select_for_update()
                .first()
            )

            if settlement:
                settlement.bank_code = validated_data["bank_code"]
                settlement.account_number = validated_data["account_number"]
                settlement.account_name = validated_data["account_name"]
                settlement.bank_name = validated_data["bank_name"]
                settlement.is_active = True
                settlement.save()
            else:
                settlement = SettlementDetail.objects.create(
                    business=business,
                    bank_code=validated_data["bank_code"],
                    account_number=validated_data["account_number"],
                    account_name=validated_data["account_name"],
                    bank_name=validated_data["bank_name"],
                    is_active=True,
                )

            OnboardingWorkflowHandler(business).complete_onboarding()

        return business


class AddSettlementDetailsChangeRequestSerializer(AddSettlementDetailsSerializer):

    def validate(self, attrs):
        attrs = super().validate(attrs)

        business = self.context["business"]
        current_settlement = (
            SettlementDetail.objects.filter(business=business, is_active=True)
            .order_by("-created_at")
            .first()
        )

        old_data, new_data = {}, {}
        for field in ["account_number", "bank_code", "account_name", "bank_name"]:
            new_value = attrs.get(field)
            old_value = (
                getattr(current_settlement, field, None) if current_settlement else None
            )

            if old_value != new_value:
                old_data[field] = old_value
                new_data[field] = new_value

        if not new_data:
            raise serializers.ValidationError("No changes detected.")

        if BusinessChangeRequest.objects.filter(
            business=business,
            section=BusinessSection.SettlementDetails.value,
            status=ChangeRequestStatus.Pending.value,
        ).exists():
            raise serializers.ValidationError(
                "A pending request for settlement details already exists."
            )

        attrs["__old_data__"] = old_data
        attrs["__new_data__"] = new_data

        return attrs

    def create(self, validated_data):
        business = self.context["business"]
        user = business.owner

        old_data = validated_data.pop("__old_data__")
        new_data = validated_data.pop("__new_data__")

        return BusinessChangeRequest.objects.create(
            business=business,
            section=BusinessSection.SettlementDetails.value,
            old_value=old_data,
            new_value=new_data,
            status=ChangeRequestStatus.Pending.value,
            created_by=user,
        )


class VerifyAccountNumberSerializer(serializers.Serializer):
    bank_code = serializers.CharField()
    account_number = serializers.CharField()

    def validate(self, attrs):
        data = super().validate(attrs)
        account_number = data["account_number"]
        bank_code = data["bank_code"]

        if len(account_number) != 10:
            raise serializers.ValidationError(
                {"account_number": "Invalid account number"}
            )

        if len(bank_code) not in (3, 6):
            raise serializers.ValidationError({"bank_code": "Invalid bank code"})

        # TODO: Talk to vas-gate for account number verification
        data["account_name"] = "Sage Dev Test Account"
        data["bank_name"] = "Sagecloud Test Bank"

        return data

    def create(self, validated_data):
        return validated_data


class GeneratePrivateKeySerializer(serializers.Serializer):
    otp = serializers.CharField(required=True)

    def save(self, **kwargs):
        user = self.context["user"]
        otp = self.validated_data["otp"]

        match user.two_factor_auth_type:
            case Auth2FATypeEnums.TOTP:
                totp = pyotp.TOTP(user.plain_two_factor_auth_secret).now()
                if str(totp) != str(otp):
                    logger.info(f"TOTP {str(totp)} != OTP {str(otp)}")
                    raise serializers.ValidationError(
                        {
                            "otp": "OTP is invalid or expired. Check your Authenticator app"
                        }
                    )
            case _:
                raise serializers.ValidationError(
                    {"user": "Invalid authentication type"}
                )

        business: Business = user.business
        private_key = APIConfigHandler().save_private_key(business)

        return private_key


class APIConfigSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = APIConfig
        fields = ("webhook_url", "webhook_signature", "whitelisted_ips")

    def create(self, validated_data):
        business = self.context["business"]
        APIConfig.objects.update_or_create(business=business, defaults=validated_data)

        return validated_data


class BusinessChangeRequestDetailSerializer(serializers.ModelSerializer):
    values = serializers.SerializerMethodField()
    initiator = serializers.SerializerMethodField()

    def get_initiator(self, obj):
        initiator: User = obj.created_by
        initiator_obj = {
            "email": initiator.email,
            "name": initiator.fullname,
            "role": initiator.role,
        }
        return initiator_obj if initiator_obj else None

    class Meta:
        model = BusinessChangeRequest
        fields = (
            "id",
            "section",
            "status",
            "object_id",
            "change_type",
            "values",
            "rejection_note",
            "initiator",
            "created_at",
            "updated_at",
        )

    def get_values(self, obj):
        section = obj.section
        match section:
            case BusinessSection.BusinessInformation.value:
                return self._build_field_values(
                    obj=obj,
                    instance=obj.business,
                    field_labels=BUSINESS_INFO_FIELD_LABELS,
                )
            case BusinessSection.SettlementDetails.value:
                settlement = SettlementDetail.objects.filter(
                    business=obj.business, is_active=True
                ).first()
                return self._build_field_values(
                    obj=obj, instance=settlement, field_labels=SETTLEMENT_FIELD_LABELS
                )
            case BusinessSection.DirectorsAndOwners.value:
                director = (
                    Director.objects.filter(pk=obj.object_id).first()
                    if obj.object_id
                    else None
                )
                return self._build_field_values(
                    obj=obj, instance=director, field_labels=DIRECTOR_FIELD_LABELS
                )
            case _:
                if section.startswith("Documentation_"):
                    document = (
                        Document.objects.filter(pk=obj.object_id).first()
                        if obj.object_id
                        else None
                    )
                    return self._build_field_values(
                        obj=obj,
                        instance=document,
                        field_labels=DOCUMENTATION_FIELD_LABELS,
                    )
                return []

    @staticmethod
    def _build_field_values(obj, instance, field_labels: dict) -> list[dict]:
        values = []

        new_values = obj.new_value or {}
        old_values = obj.old_value or {}

        for field, label in field_labels.items():
            old_val = old_values.get(field)
            new_val = new_values.get(field)

            if old_val is None and instance is not None:
                attr = getattr(instance, field, None)
                if field == "nin_document" and attr is not None:
                    try:
                        old_val = attr.document.url if attr.document else None
                    except Exception:
                        old_val = None
                else:
                    old_val = attr

            if isinstance(new_val, dict):  # THIS IS EXPECTED WITH DOCUMENTS
                new_val = new_val.get("url", None)

            updated = old_val != new_val and new_val is not None

            value_entry = {
                "field": field,
                "label": label,
                "old_value": old_val,
                "new_value": new_val,
                "updated": updated,
            }

            values.append(value_entry)
        return values


class BusinessChangeRequestMinimalSerializer(serializers.ModelSerializer):
    initiator = serializers.SerializerMethodField()

    def get_initiator(self, obj):
        initiator: User = obj.created_by
        initiator_obj = {
            "email": initiator.email,
            "name": initiator.fullname,
            "role": initiator.role,
        }
        return initiator_obj if initiator_obj else None

    class Meta:
        model = BusinessChangeRequest
        fields = (
            "id",
            "section",
            "old_value",
            "new_value",
            "status",
            "object_id",
            "change_type",
            "rejection_note",
            "initiator",
            "created_at",
            "updated_at",
        )


class BusinessInformationChangeRequestSerializer(BusinessInformationSerializer):
    def validate(self, attrs):
        business = self.instance

        phone = attrs.get("phone")
        if phone:
            normalized_phone = clean_phone_number(phone.strip())
            if (
                Business.objects.filter(phone=normalized_phone)
                .exclude(pk=business.pk)
                .exists()
            ):
                raise serializers.ValidationError(
                    {"phone": "Phone number already exists"}
                )
            attrs["phone"] = normalized_phone

        email = attrs.get("email")
        if email:
            try:
                validated_email = validate_email(email)
                normalized_email = validated_email.normalized
            except EmailNotValidError as e:
                raise serializers.ValidationError({"email": str(e)})

            if (
                Business.objects.filter(email=normalized_email)
                .exclude(pk=business.pk)
                .exists()
            ):
                raise serializers.ValidationError({"email": "Email already exists"})
            attrs["email"] = normalized_email

        return attrs

    def update(self, instance, validated_data):
        business = instance
        user = business.owner

        old_data = {}
        new_data = {}

        for field, new_value in validated_data.items():
            current_value = getattr(business, field)
            if current_value != new_value:
                old_data[field] = current_value
                new_data[field] = new_value

        if not new_data:
            raise serializers.ValidationError("No changes detected")

        existing_request = BusinessChangeRequest.objects.filter(
            business=business,
            section=BusinessSection.BusinessInformation.value,
            status=ChangeRequestStatus.Pending.value,
        ).first()

        if existing_request:
            raise serializers.ValidationError(
                "A pending request for business information already exists."
            )

        change_request = BusinessChangeRequest.objects.create(
            business=business,
            section=BusinessSection.BusinessInformation.value,
            old_value=old_data,
            new_value=new_data,
            status=ChangeRequestStatus.Pending.value,
            created_by=user,
        )

        return change_request
