import django_filters
from business.enums import BusinessStatus
from business.models import Business
from common.filter import DateFilter
from django.db.models import Q


class BusinessFilter(DateFilter):
    name = django_filters.CharFilter(field_name="name", lookup_expr="icontains")
    phone = django_filters.CharFilter(field_name="phone", lookup_expr="icontains")
    rc_number = django_filters.CharFilter(
        field_name="rc_number", lookup_expr="icontains"
    )

    email = django_filters.CharFilter(method="filter_by_email")

    status = django_filters.ChoiceFilter(
        field_name="status",
        choices=BusinessStatus.choices(),
    )

    def filter_by_email(self, queryset, name, value):
        return queryset.filter(
            Q(email__icontains=value) | Q(owner__email__icontains=value)
        )

    class Meta:
        model = Business
        fields = [
            "name",
            "email",
            "phone",
            "rc_number",
            "status",
        ]
