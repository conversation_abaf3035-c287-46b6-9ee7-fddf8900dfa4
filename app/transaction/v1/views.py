from common.pagination import LargeDatasetKeySetPagination
from django.db.models import Count, Q, Sum
from django.http import Http404
from django.utils.dateparse import parse_date
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import OpenApiParameter, OpenApiTypes, extend_schema
from rest_framework import filters, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from transaction.enums import TransactionClassEnum, TransactionStatusEnum
from transaction.models import Transaction
from transaction.v1.filters import TransactionFilter
from transaction.v1.serializers import (
    TransactionOverviewSerializer,
    TransactionPolymorphicSerializer,
    TransactionSerializer,
)


class TransactionViewSet(viewsets.ModelViewSet):
    queryset = Transaction.objects.all().select_related("wallet", "business")
    permission_classes = [IsAuthenticated]
    serializer_class = TransactionSerializer
    http_method_names = ["get"]
    lookup_field = "identifier"
    pagination_class = LargeDatasetKeySetPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = TransactionFilter
    search_fields = ["reference", "merchant_reference"]
    ordering_fields = ["-created_at"]

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == "Business_Owner":
            queryset = queryset.filter(business__owner=self.request.user)
        return queryset

    def get_object(self):
        lookup_value = self.kwargs.get(self.lookup_field)
        for field in ["id", "reference", "merchant_reference"]:
            try:
                return self.get_queryset().get(**{field: lookup_value})
            except Transaction.DoesNotExist:
                continue

        raise Http404("Transaction not found.")

    def get_serializer_class(self):
        if self.action == "retrieve":
            return TransactionPolymorphicSerializer
        return super().get_serializer_class()

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="start",
                required=True,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
                description="Start date in format YYYY-MM-DD",
            ),
            OpenApiParameter(
                name="end",
                required=True,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
                description="End date in format YYYY-MM-DD",
            ),
            OpenApiParameter(
                name="txn_class",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                enum=TransactionClassEnum.values(),
                description="Transaction Class",
            ),
        ],
        responses={200: TransactionOverviewSerializer},
    )
    @action(detail=False, methods=["get"], url_path="overview")
    def overview(self, request):
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")
        txn_class = request.query_params.get("txn_class")

        if not start_date or not end_date:
            raise ValidationError("Both start_date and end_date are required.")

        try:
            start = parse_date(start_date)
            end = parse_date(end_date)
            if not start or not end:
                raise ValueError
        except ValueError:
            raise ValidationError("Dates must be in YYYY-MM-DD format.")

        queryset = self.get_queryset().filter(created_at__date__range=(start, end))
        if txn_class:
            queryset = queryset.filter(txn_class=txn_class)

        totals = queryset.aggregate(
            total_value=Sum("amount"),
            total_count=Count("id"),
            total_commission=Sum(
                "revenue"
            ),  # TODO: Fix this and add commission from computed balance
            pending_count=Count(
                "id", filter=Q(status=TransactionStatusEnum.PENDING.value)
            ),
            failed_count=Count(
                "id", filter=Q(status=TransactionStatusEnum.FAILED.value)
            ),
            successful_count=Count(
                "id", filter=Q(status=TransactionStatusEnum.SUCCESSFUL.value)
            ),
        )

        return Response(
            {
                "total_value": totals["total_value"] or 0,
                "total_count": totals["total_count"],
                "total_commission": totals["total_commission"],
                "pending_count": totals["pending_count"],
                "failed_count": totals["failed_count"],
                "successful_count": totals["successful_count"],
            }
        )
