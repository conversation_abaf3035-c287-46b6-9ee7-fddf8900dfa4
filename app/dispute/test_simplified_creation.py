#!/usr/bin/env python3
"""
Test script for the new simplified dispute creation feature.

This script demonstrates how to create disputes using only transaction reference and message.

Usage:
    python test_simplified_creation.py

Make sure to:
1. Have the Django server running on localhost:47001
2. Have seeded test data with your account
"""

import json
import requests
from datetime import datetime


class SimplifiedDisputeAPITester:
    def __init__(self, base_url="http://localhost:47001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token = None
        
    def login(self, email, password):
        """Login and get authentication token."""
        login_url = f"{self.base_url}/api/v1/auth/login/"
        data = {
            "email": email,
            "password": password
        }
        
        print(f"🔐 Logging in as {email}...")
        response = self.session.post(login_url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            self.auth_token = result.get('data', {}).get('access_token')
            self.session.headers.update({
                'Authorization': f'Bearer {self.auth_token}'
            })
            print(f"✅ Login successful!")
            return True
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(response.text)
            return False
    
    def validate_transaction_reference(self, reference):
        """Test validating a transaction reference."""
        url = f"{self.base_url}/api/v1/disputes/validate-reference/"
        
        data = {
            "transaction_reference": reference
        }
        
        print(f"\n🔍 Validating transaction reference: {reference}")
        print("Request Data:")
        print(json.dumps(data, indent=2))
        
        response = self.session.post(url, json=data)
        
        print(f"\nResponse Status: {response.status_code}")
        print("Response Data:")
        print(json.dumps(response.json(), indent=2))
        
        if response.status_code == 200:
            print("✅ Transaction reference is valid!")
            return response.json()['data']
        else:
            print("❌ Transaction reference validation failed")
            return None
    
    def create_dispute_from_reference(self, reference, message):
        """Test creating a dispute from transaction reference."""
        url = f"{self.base_url}/api/v1/disputes/create-from-reference/"
        
        data = {
            "transaction_reference": reference,
            "message": message
        }
        
        print(f"\n📝 Creating dispute from transaction reference...")
        print("Request Data:")
        print(json.dumps(data, indent=2))
        
        response = self.session.post(url, json=data)
        
        print(f"\nResponse Status: {response.status_code}")
        print("Response Data:")
        print(json.dumps(response.json(), indent=2))
        
        if response.status_code == 201:
            dispute_id = response.json()['data']['id']
            print(f"✅ Dispute created successfully with ID: {dispute_id}")
            return dispute_id
        else:
            print("❌ Failed to create dispute")
            return None
    
    def test_invalid_reference(self):
        """Test with an invalid transaction reference."""
        print("\n🧪 Testing with invalid transaction reference...")
        
        invalid_ref = "INVALID_TXN_12345"
        result = self.validate_transaction_reference(invalid_ref)
        
        if result is None:
            print("✅ Invalid reference properly rejected")
        else:
            print("❌ Invalid reference was accepted (unexpected)")
    
    def test_empty_message(self, reference):
        """Test with empty or short message."""
        url = f"{self.base_url}/api/v1/disputes/create-from-reference/"
        
        print("\n🧪 Testing with invalid message...")
        
        data = {
            "transaction_reference": reference,
            "message": "Short"  # Too short message
        }
        
        response = self.session.post(url, json=data)
        
        print(f"Response Status: {response.status_code}")
        print("Response Data:")
        print(json.dumps(response.json(), indent=2))
        
        if response.status_code == 400:
            print("✅ Short message properly rejected")
        else:
            print("❌ Short message was accepted (unexpected)")
    
    def compare_creation_methods(self, reference):
        """Compare simplified vs full creation methods."""
        print("\n🔄 Comparing creation methods...")
        
        # Method 1: Simplified creation
        message = "Transaction failed but amount was debited. Please investigate and refund."
        dispute_id_1 = self.create_dispute_from_reference(reference, message)
        
        if dispute_id_1:
            # Get the created dispute details
            detail_url = f"{self.base_url}/api/v1/disputes/{dispute_id_1}/"
            response = self.session.get(detail_url)
            
            if response.status_code == 200:
                dispute_data = response.json()['data']
                print("\n📊 Simplified Creation Result:")
                print(f"   Transaction Reference: {dispute_data['transaction_reference']}")
                print(f"   VAS Service: {dispute_data['vas_service']}")
                print(f"   Amount: {dispute_data['amount']}")
                print(f"   Merchant Name: {dispute_data['merchant_name']}")
                print(f"   Status: {dispute_data['status']}")
                print("   ✅ All transaction details automatically populated!")
    
    def run_comprehensive_test(self):
        """Run comprehensive test of the simplified dispute creation."""
        print("🚀 Starting Simplified Dispute Creation Test Suite")
        print("=" * 60)
        
        # Login with your account
        if not self.login("<EMAIL>", "your_password_here"):
            print("❌ Please update the password in the script and try again")
            return
        
        # Test transaction references from your seeded data
        test_references = [
            "TXN_KOLADEG_0001",
            "TXN_KOLADEG_0002", 
            "TXN_KOLADEG_0003"
        ]
        
        valid_reference = None
        
        # Find a valid reference
        for ref in test_references:
            print(f"\n🔍 Testing reference: {ref}")
            if self.validate_transaction_reference(ref):
                valid_reference = ref
                break
        
        if not valid_reference:
            print("❌ No valid transaction references found. Please check your seeded data.")
            return
        
        print(f"\n✅ Using valid reference: {valid_reference}")
        
        # Test simplified dispute creation
        message = "Transaction failed but amount was debited from my account. Please investigate and refund immediately."
        dispute_id = self.create_dispute_from_reference(valid_reference, message)
        
        # Test validation with invalid reference
        self.test_invalid_reference()
        
        # Test with invalid message
        self.test_empty_message(valid_reference)
        
        # Compare creation methods
        if len(test_references) > 1:
            self.compare_creation_methods(test_references[1])
        
        print("\n" + "=" * 60)
        print("🎉 Simplified Dispute Creation Test Suite Completed!")
        print("\n📋 Summary:")
        print("   ✅ Transaction reference validation")
        print("   ✅ Simplified dispute creation")
        print("   ✅ Automatic transaction data population")
        print("   ✅ Error handling for invalid inputs")
        print("   ✅ Business isolation and security")


def main():
    """Main function to run the tests."""
    print("⚠️  IMPORTANT: Please update the password in the login method before running!")
    print("   Change 'your_password_here' to your actual <NAME_EMAIL>")
    print()
    
    tester = SimplifiedDisputeAPITester()
    
    # You need to update this with your actual password
    # tester.run_comprehensive_test()
    
    # For now, just show the usage
    print("📖 Usage Instructions:")
    print("1. Update the password in the login method")
    print("2. Make sure your server is running on localhost:47001")
    print("3. Ensure you have seeded test data with your account")
    print("4. Run: python test_simplified_creation.py")
    print()
    print("🔗 API Endpoints Added:")
    print("   POST /api/v1/disputes/create-from-reference/")
    print("   POST /api/v1/disputes/validate-reference/")
    print()
    print("📝 Example Usage:")
    print("""
    # Validate transaction reference
    curl -X POST http://localhost:47001/api/v1/disputes/validate-reference/ \\
      -H "Authorization: Bearer YOUR_TOKEN" \\
      -H "Content-Type: application/json" \\
      -d '{"transaction_reference": "TXN_KOLADEG_0001"}'
    
    # Create dispute from reference
    curl -X POST http://localhost:47001/api/v1/disputes/create-from-reference/ \\
      -H "Authorization: Bearer YOUR_TOKEN" \\
      -H "Content-Type: application/json" \\
      -d '{
        "transaction_reference": "TXN_KOLADEG_0001",
        "message": "Transaction failed but amount was debited. Please investigate."
      }'
    """)


if __name__ == "__main__":
    main()
