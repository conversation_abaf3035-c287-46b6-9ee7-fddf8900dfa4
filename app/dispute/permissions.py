from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied

from .enums import BusinessR<PERSON>, DisputePermission, ROLE_PERMISSIONS
from .models import BusinessMember


class IsBusinessOwner(permissions.BasePermission):
    """Allows access only to business owners."""
    
    message = "Only business owners are authorized to perform this action."

    def has_permission(self, request, view):
        return bool(
            request.user 
            and request.user.is_authenticated 
            and request.user.role == "Business_Owner"
        )


class HasDisputePermission(permissions.BasePermission):
    """Check if user has specific dispute permission based on their business role."""
    
    def __init__(self, required_permission):
        self.required_permission = required_permission
        super().__init__()

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Get user's business (either as owner or team member)
        business = self._get_user_business(request.user)
        if not business:
            return False
        
        # Get user's role in the business
        user_role = self._get_user_business_role(request.user, business)
        if not user_role:
            return False
        
        # Check if role has required permission
        role_permissions = ROLE_PERMISSIONS.get(user_role, [])
        return self.required_permission in role_permissions

    def has_object_permission(self, request, view, obj):
        """Check object-level permissions for disputes."""
        if not self.has_permission(request, view):
            return False
        
        # Ensure dispute belongs to user's business
        business = self._get_user_business(request.user)
        return obj.business == business

    def _get_user_business(self, user):
        """Get the business associated with the user."""
        # Check if user is a business owner
        if hasattr(user, 'business'):
            return user.business
        
        # Check if user is a team member
        try:
            membership = BusinessMember.objects.get(user=user, is_active=True)
            return membership.business
        except BusinessMember.DoesNotExist:
            return None

    def _get_user_business_role(self, user, business):
        """Get user's role in the business."""
        # If user is the business owner
        if hasattr(user, 'business') and user.business == business:
            return BusinessRole.BUSINESS_OWNER
        
        # If user is a team member
        try:
            membership = BusinessMember.objects.get(
                user=user, 
                business=business, 
                is_active=True
            )
            return getattr(BusinessRole, membership.role)
        except (BusinessMember.DoesNotExist, AttributeError):
            return None


class CanCreateDispute(HasDisputePermission):
    """Permission to create disputes."""
    
    def __init__(self):
        super().__init__(DisputePermission.CREATE_DISPUTE)


class CanReadDispute(HasDisputePermission):
    """Permission to read disputes."""
    
    def __init__(self):
        super().__init__(DisputePermission.READ_DISPUTE)


class CanReadAllBusinessDisputes(HasDisputePermission):
    """Permission to read all disputes in the business."""
    
    def __init__(self):
        super().__init__(DisputePermission.READ_ALL_BUSINESS_DISPUTES)


def get_user_business_context(user):
    """Helper function to get user's business context."""
    if not user or not user.is_authenticated:
        return None, None
    
    # Check if user is a business owner
    if hasattr(user, 'business'):
        return user.business, BusinessRole.BUSINESS_OWNER
    
    # Check if user is a team member
    try:
        membership = BusinessMember.objects.get(user=user, is_active=True)
        role = getattr(BusinessRole, membership.role, None)
        return membership.business, role
    except BusinessMember.DoesNotExist:
        return None, None


def check_dispute_permission(user, permission, dispute=None):
    """Check if user has specific dispute permission."""
    business, role = get_user_business_context(user)
    
    if not business or not role:
        return False
    
    # Check if dispute belongs to user's business (if dispute is provided)
    if dispute and dispute.business != business:
        return False
    
    # Check role permissions
    role_permissions = ROLE_PERMISSIONS.get(role, [])
    return permission in role_permissions
