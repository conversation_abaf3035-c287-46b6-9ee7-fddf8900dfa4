import django_filters
from django.db import models

from .enums import DisputeStatus
from .models import Dispute


class DisputeFilter(django_filters.FilterSet):
    """Filter for disputes."""
    
    status = django_filters.ChoiceFilter(
        choices=DisputeStatus.choices(),
        help_text="Filter by dispute status"
    )
    
    vas_service = django_filters.CharFilter(
        lookup_expr='iexact',
        help_text="Filter by VAS service type"
    )
    
    transaction_reference = django_filters.CharFilter(
        lookup_expr='icontains',
        help_text="Filter by transaction reference (partial match)"
    )
    
    merchant_name = django_filters.CharFilter(
        lookup_expr='icontains',
        help_text="Filter by merchant name (partial match)"
    )
    
    amount_min = django_filters.NumberFilter(
        field_name='amount',
        lookup_expr='gte',
        help_text="Filter by minimum amount"
    )
    
    amount_max = django_filters.NumberFilter(
        field_name='amount',
        lookup_expr='lte',
        help_text="Filter by maximum amount"
    )
    
    created_at_after = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='gte',
        help_text="Filter disputes created after this date"
    )
    
    created_at_before = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='lte',
        help_text="Filter disputes created before this date"
    )
    
    transaction_date_after = django_filters.DateTimeFilter(
        field_name='transaction_date',
        lookup_expr='gte',
        help_text="Filter by transaction date after"
    )
    
    transaction_date_before = django_filters.DateTimeFilter(
        field_name='transaction_date',
        lookup_expr='lte',
        help_text="Filter by transaction date before"
    )

    class Meta:
        model = Dispute
        fields = [
            'status', 'vas_service', 'transaction_reference', 'merchant_name',
            'amount_min', 'amount_max', 'created_at_after', 'created_at_before',
            'transaction_date_after', 'transaction_date_before'
        ]
