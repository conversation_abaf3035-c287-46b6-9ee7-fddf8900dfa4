# Disputes API Documentation - Updated with Simplified Creation

## Overview

The Disputes API now supports two ways to create disputes:
1. **Full Details**: Provide all transaction details manually
2. **From Reference**: Provide only transaction reference ID and message (NEW!)

## Base URL
```
/api/v1/disputes/
```

## Authentication
All endpoints require authentication. Include the authorization header:
```
Authorization: Bearer <your_token>
```

---

## Endpoints

### 1. Create Dispute from Transaction Reference (NEW!)

**POST** `/api/v1/disputes/create-from-reference/`

Create a dispute using only the transaction reference ID and message. The system will automatically fetch all transaction details.

#### Request Body:
```json
{
    "transaction_reference": "TXN_KOLADEG_0001",
    "message": "Transaction failed but amount was debited from my account. Please investigate and refund."
}
```

#### Response (201 Created):
```json
{
    "success": true,
    "message": "Dispute created successfully from transaction reference",
    "data": {
        "id": "dispute_123456789",
        "transaction_reference": "TXN_KOLADEG_0001",
        "vas_service": "AIRTIME",
        "vas_service_display": "Airtime",
        "amount": "1500.00",
        "charge": "22.50",
        "stamp_duty": "50.00",
        "previous_balance": "5000.00",
        "new_balance": "3427.50",
        "transaction_date": "2024-12-18T10:30:00Z",
        "status": "PENDING",
        "status_display": "Pending",
        "merchant_name": "Ko's Business",
        "message": "Transaction failed but amount was debited from my account. Please investigate and refund.",
        "created_by_name": "Glory Kolade",
        "created_by_email": "<EMAIL>",
        "business_name": "Ko's Business",
        "resolved_by_name": null,
        "resolved_at": null,
        "resolution_notes": null,
        "created_at": "2024-12-18T11:00:00Z",
        "updated_at": "2024-12-18T11:00:00Z"
    }
}
```

---

### 2. Validate Transaction Reference (NEW!)

**POST** `/api/v1/disputes/validate-reference/`

Validate a transaction reference and get transaction information before creating a dispute.

#### Request Body:
```json
{
    "transaction_reference": "TXN_KOLADEG_0001"
}
```

#### Response (200 OK):
```json
{
    "success": true,
    "message": "Transaction found",
    "data": {
        "reference": "TXN_KOLADEG_0001",
        "vas_service": "AIRTIME",
        "amount": "1500.00",
        "merchant_name": "Ko's Business",
        "transaction_date": "2024-12-18T10:30:00Z",
        "status": "SUCCESSFUL"
    }
}
```

#### Error Response (404 Not Found):
```json
{
    "success": false,
    "message": "Transaction with reference 'TXN_INVALID_001' not found.",
    "data": null
}
```

---

### 3. Create Dispute (Full Details)

**POST** `/api/v1/disputes/`

Create a new dispute by providing all transaction details manually.

#### Request Body:
```json
{
    "transaction_reference": "TXN_20241218_001234",
    "vas_service": "AIRTIME",
    "amount": "1500.00",
    "charge": "22.50",
    "stamp_duty": "50.00",
    "previous_balance": "5000.00",
    "new_balance": "3427.50",
    "transaction_date": "2024-12-18T10:30:00Z",
    "merchant_name": "Test Business Ltd",
    "message": "Transaction failed but amount was debited from my account. Please investigate and refund."
}
```

#### Response (201 Created):
```json
{
    "success": true,
    "message": "Dispute created successfully",
    "data": {
        "id": "dispute_123456789",
        "transaction_reference": "TXN_20241218_001234",
        "vas_service": "AIRTIME",
        "vas_service_display": "Airtime",
        "amount": "1500.00",
        "charge": "22.50",
        "stamp_duty": "50.00",
        "previous_balance": "5000.00",
        "new_balance": "3427.50",
        "transaction_date": "2024-12-18T10:30:00Z",
        "status": "PENDING",
        "status_display": "Pending",
        "merchant_name": "Test Business Ltd",
        "message": "Transaction failed but amount was debited from my account. Please investigate and refund.",
        "created_by_name": "John Doe",
        "created_by_email": "<EMAIL>",
        "business_name": "Test Business Ltd",
        "resolved_by_name": null,
        "resolved_at": null,
        "resolution_notes": null,
        "created_at": "2024-12-18T11:00:00Z",
        "updated_at": "2024-12-18T11:00:00Z"
    }
}
```

---

### 4. List Disputes

**GET** `/api/v1/disputes/`

Retrieve a paginated list of disputes for the user's business.

#### Query Parameters:
- `page` (int): Page number (default: 1)
- `page_size` (int): Items per page (default: 20)
- `status` (string): Filter by status (PENDING, IN_REVIEW, RESOLVED)
- `vas_service` (string): Filter by VAS service type
- `transaction_reference` (string): Filter by transaction reference (partial match)
- `merchant_name` (string): Filter by merchant name (partial match)
- `amount_min` (decimal): Filter by minimum amount
- `amount_max` (decimal): Filter by maximum amount
- `created_at_after` (datetime): Filter disputes created after this date
- `created_at_before` (datetime): Filter disputes created before this date
- `search` (string): Search in transaction reference, merchant name, or message

#### Example Request:
```
GET /api/v1/disputes/?status=PENDING&page=1&page_size=10
```

#### Response (200 OK):
```json
{
    "success": true,
    "message": "Disputes retrieved successfully",
    "data": {
        "count": 25,
        "next": "http://localhost:47001/api/v1/disputes/?page=2",
        "previous": null,
        "results": [
            {
                "id": "dispute_123456789",
                "transaction_reference": "TXN_KOLADEG_0001",
                "vas_service": "AIRTIME",
                "vas_service_display": "Airtime",
                "amount": "1500.00",
                "status": "PENDING",
                "status_display": "Pending",
                "merchant_name": "Ko's Business",
                "created_by_name": "Glory Kolade",
                "business_name": "Ko's Business",
                "created_at": "2024-12-18T11:00:00Z",
                "updated_at": "2024-12-18T11:00:00Z"
            }
        ]
    }
}
```

---

### 5. Get Dispute Details

**GET** `/api/v1/disputes/{dispute_id}/`

Retrieve detailed information about a specific dispute.

#### Response (200 OK):
```json
{
    "success": true,
    "message": "Dispute retrieved successfully",
    "data": {
        "id": "dispute_123456789",
        "transaction_reference": "TXN_KOLADEG_0001",
        "vas_service": "AIRTIME",
        "vas_service_display": "Airtime",
        "amount": "1500.00",
        "charge": "22.50",
        "stamp_duty": "50.00",
        "previous_balance": "5000.00",
        "new_balance": "3427.50",
        "transaction_date": "2024-12-18T10:30:00Z",
        "status": "PENDING",
        "status_display": "Pending",
        "merchant_name": "Ko's Business",
        "message": "Transaction failed but amount was debited from my account. Please investigate and refund.",
        "created_by_name": "Glory Kolade",
        "created_by_email": "<EMAIL>",
        "business_name": "Ko's Business",
        "resolved_by_name": null,
        "resolved_at": null,
        "resolution_notes": null,
        "created_at": "2024-12-18T11:00:00Z",
        "updated_at": "2024-12-18T11:00:00Z"
    }
}
```

---

### 6. Update Dispute Status

**PATCH** `/api/v1/disputes/{dispute_id}/update-status/`

Update the status of a dispute. Only users with appropriate permissions can perform this action.

#### Request Body:
```json
{
    "status": "IN_REVIEW",
    "resolution_notes": "Dispute is now under investigation by our technical team."
}
```

#### Response (200 OK):
```json
{
    "success": true,
    "message": "Dispute status updated successfully",
    "data": {
        "id": "dispute_123456789",
        "transaction_reference": "TXN_KOLADEG_0001",
        "vas_service": "AIRTIME",
        "vas_service_display": "Airtime",
        "amount": "1500.00",
        "charge": "22.50",
        "stamp_duty": "50.00",
        "previous_balance": "5000.00",
        "new_balance": "3427.50",
        "transaction_date": "2024-12-18T10:30:00Z",
        "status": "IN_REVIEW",
        "status_display": "In Review",
        "merchant_name": "Ko's Business",
        "message": "Transaction failed but amount was debited from my account. Please investigate and refund.",
        "created_by_name": "Glory Kolade",
        "created_by_email": "<EMAIL>",
        "business_name": "Ko's Business",
        "resolved_by_name": null,
        "resolved_at": null,
        "resolution_notes": "Dispute is now under investigation by our technical team.",
        "created_at": "2024-12-18T11:00:00Z",
        "updated_at": "2024-12-18T11:15:00Z"
    }
}
```

---

### 7. Get Dispute Statistics

**GET** `/api/v1/disputes/statistics/`

Get dispute statistics for the user's business.

#### Response (200 OK):
```json
{
    "success": true,
    "message": "Dispute statistics retrieved successfully",
    "data": {
        "total_disputes": 45,
        "pending_disputes": 12,
        "in_review_disputes": 8,
        "resolved_disputes": 25,
        "business_name": "Ko's Business",
        "business_id": "business_987654321"
    }
}
```

---

## Usage Examples

### Create Dispute from Transaction Reference (Recommended)

This is the easiest way to create a dispute. You only need the transaction reference and a message:

```bash
curl -X POST http://localhost:47001/api/v1/disputes/create-from-reference/ \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_reference": "TXN_KOLADEG_0001",
    "message": "Transaction failed but amount was debited from my account. Please investigate and refund."
  }'
```

### Validate Transaction Reference First

Before creating a dispute, you can validate the transaction reference:

```bash
curl -X POST http://localhost:47001/api/v1/disputes/validate-reference/ \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_reference": "TXN_KOLADEG_0001"
  }'
```

### List Your Disputes

```bash
curl -X GET "http://localhost:47001/api/v1/disputes/?status=PENDING&page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## Key Features

### ✅ **Simplified Dispute Creation**
- Create disputes with just transaction reference + message
- Automatic transaction data lookup
- Validation ensures transaction belongs to your business

### ✅ **Transaction Validation**
- Validate transaction references before creating disputes
- Get transaction preview information
- Proper error handling for invalid references

### ✅ **Backward Compatibility**
- Original full-detail creation method still works
- Existing API endpoints unchanged
- Same response format for all creation methods

### ✅ **Smart Transaction Lookup**
- Searches across all transaction models (Transaction, VAS transactions)
- Handles both reference and merchant_reference fields
- Business isolation ensures security

---

## Error Handling

### Common Error Responses:

#### Transaction Not Found:
```json
{
    "success": false,
    "message": "Transaction with reference 'TXN_INVALID_001' not found in your business.",
    "data": null
}
```

#### Invalid Message:
```json
{
    "success": false,
    "message": "Validation error",
    "errors": {
        "message": ["Message must be at least 10 characters long."]
    }
}
```

#### Permission Denied:
```json
{
    "success": false,
    "message": "You do not have permission to perform this action."
}
```

---

## Testing with Your Account

You can now test the new simplified dispute creation with your seeded data:

1. **Login with your account**: `<EMAIL>`
2. **Use transaction references** like: `TXN_KOLADEG_0001`, `TXN_KOLADEG_0002`, etc.
3. **Create disputes easily** with just reference + message

The new feature makes dispute creation much more user-friendly while maintaining all the existing functionality!
