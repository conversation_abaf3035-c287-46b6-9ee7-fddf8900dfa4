from decimal import Decimal
from typing import Dict, Optional, Union

from django.db.models import Q
from rest_framework.exceptions import Validation<PERSON>rror
from transaction.models import Transaction
from transaction.models.airtime import AirtimeVASTransaction
from transaction.models.betting import BettingVASTransaction
from transaction.models.cable_tv import CableTVVASTransaction
from transaction.models.data import DataVASTransaction
from transaction.models.education import EducationVASTransaction
from transaction.models.electricity import ElectricityVASTransaction
from transaction.models.epin import EpinVASTransaction
from transaction.models.kyc import KYCVASTransaction
from transaction.models.virtual_account import VirtualAccountVasTransaction


class TransactionLookupService:
    """Service to lookup transaction details by reference ID."""
    
    # Map transaction classes to their VAS transaction models
    VAS_TRANSACTION_MODELS = {
        'AIRTIME': AirtimeVASTransaction,
        'DATA': DataVASTransaction,
        'BETTING': BettingVASTransaction,
        'CABLE_TV': CableTVVASTransaction,
        'ELECTRICITY': ElectricityVASTransaction,
        'EDUCATION': EducationVASTransaction,
        'KYC': KYCVASTransaction,
        'EPIN': EpinVASTransaction,
        'VIRTUAL_ACCOUNT': VirtualAccountVasTransaction,
    }
    
    @classmethod
    def find_transaction_by_reference(cls, reference: str, business=None) -> Optional[Dict]:
        """
        Find transaction by reference ID across all transaction models.
        
        Args:
            reference: Transaction reference ID
            business: Business instance to filter by (optional)
            
        Returns:
            Dictionary with transaction details or None if not found
        """
        # First try to find in the main Transaction model
        transaction = cls._find_in_main_transactions(reference, business)
        if transaction:
            return cls._format_transaction_data(transaction)
        
        # Then try VAS transaction models
        vas_transaction = cls._find_in_vas_transactions(reference, business)
        if vas_transaction:
            return cls._format_vas_transaction_data(vas_transaction)
        
        return None
    
    @classmethod
    def _find_in_main_transactions(cls, reference: str, business=None) -> Optional[Transaction]:
        """Find transaction in main Transaction model."""
        try:
            query = Q(reference=reference) | Q(merchant_reference=reference)
            if business:
                query &= Q(business=business)
            
            return Transaction.objects.select_related('business', 'wallet').get(query)
        except Transaction.DoesNotExist:
            return None
    
    @classmethod
    def _find_in_vas_transactions(cls, reference: str, business=None):
        """Find transaction in VAS transaction models."""
        for txn_class, model_class in cls.VAS_TRANSACTION_MODELS.items():
            try:
                query = Q(reference=reference) | Q(merchant_reference=reference)
                if business:
                    query &= Q(business=business)
                
                transaction = model_class.objects.select_related('business', 'wallet').get(query)
                # Add transaction class info
                transaction._txn_class = txn_class
                return transaction
            except model_class.DoesNotExist:
                continue
        return None
    
    @classmethod
    def _format_transaction_data(cls, transaction: Transaction) -> Dict:
        """Format main Transaction data for dispute creation."""
        return {
            'transaction_reference': transaction.reference,
            'vas_service': transaction.txn_class,
            'amount': transaction.amount,
            'charge': transaction.charge,
            'stamp_duty': Decimal('0.00'),  # Not available in main Transaction model
            'previous_balance': transaction.old_balance,
            'new_balance': transaction.new_balance,
            'transaction_date': transaction.created_at,
            'merchant_name': transaction.business.name or f"Business {transaction.business.id}",
            'business': transaction.business,
            'transaction_status': transaction.status,
            'transaction_mode': transaction.mode,
            'transaction_type': transaction.type,
            'narration': transaction.narration,
        }
    
    @classmethod
    def _format_vas_transaction_data(cls, transaction) -> Dict:
        """Format VAS Transaction data for dispute creation."""
        # Calculate stamp duty (typically 50 NGN for transactions > 1000)
        stamp_duty = Decimal('50.00') if transaction.amount > 1000 else Decimal('0.00')
        
        # Calculate previous balance (current balance + amount + charge + stamp_duty)
        previous_balance = transaction.wallet.balance + transaction.amount + transaction.charge + stamp_duty
        
        return {
            'transaction_reference': transaction.reference,
            'vas_service': getattr(transaction, '_txn_class', 'UNKNOWN'),
            'amount': transaction.amount,
            'charge': transaction.charge,
            'stamp_duty': stamp_duty,
            'previous_balance': previous_balance,
            'new_balance': transaction.wallet.balance,
            'transaction_date': transaction.created_at,
            'merchant_name': transaction.business.name or f"Business {transaction.business.id}",
            'business': transaction.business,
            'transaction_status': transaction.status,
            'transaction_mode': transaction.mode,
            'narration': transaction.narration,
        }


class DisputeCreationService:
    """Service to handle dispute creation from transaction reference."""
    
    @classmethod
    def create_dispute_from_reference(cls, reference: str, message: str, user, business=None):
        """
        Create a dispute using only transaction reference and message.
        
        Args:
            reference: Transaction reference ID
            message: Dispute message/description
            user: User creating the dispute
            business: Business context (optional, will be inferred from user)
            
        Returns:
            Dictionary with dispute data ready for creation
            
        Raises:
            ValidationError: If transaction not found or validation fails
        """
        # Use user's business if not provided
        if not business:
            if hasattr(user, 'business'):
                business = user.business
            else:
                # Check if user is a team member
                from .models import BusinessMember
                try:
                    membership = BusinessMember.objects.get(user=user, is_active=True)
                    business = membership.business
                except BusinessMember.DoesNotExist:
                    raise ValidationError("User is not associated with any business.")
        
        # Find transaction by reference
        transaction_data = TransactionLookupService.find_transaction_by_reference(
            reference, business
        )
        
        if not transaction_data:
            raise ValidationError(
                f"Transaction with reference '{reference}' not found in your business."
            )
        
        # Verify transaction belongs to user's business
        if transaction_data['business'] != business:
            raise ValidationError(
                "Transaction does not belong to your business."
            )
        
        # Prepare dispute data
        dispute_data = {
            'transaction_reference': transaction_data['transaction_reference'],
            'vas_service': transaction_data['vas_service'],
            'amount': transaction_data['amount'],
            'charge': transaction_data['charge'],
            'stamp_duty': transaction_data['stamp_duty'],
            'previous_balance': transaction_data['previous_balance'],
            'new_balance': transaction_data['new_balance'],
            'transaction_date': transaction_data['transaction_date'],
            'merchant_name': transaction_data['merchant_name'],
            'message': message,
            'business': business,
            'created_by': user,
        }
        
        return dispute_data
    
    @classmethod
    def validate_transaction_reference(cls, reference: str, business=None) -> Dict:
        """
        Validate transaction reference and return transaction info.
        
        Args:
            reference: Transaction reference ID
            business: Business to filter by
            
        Returns:
            Dictionary with transaction info
            
        Raises:
            ValidationError: If transaction not found
        """
        transaction_data = TransactionLookupService.find_transaction_by_reference(
            reference, business
        )
        
        if not transaction_data:
            raise ValidationError(
                f"Transaction with reference '{reference}' not found."
            )
        
        return {
            'reference': transaction_data['transaction_reference'],
            'vas_service': transaction_data['vas_service'],
            'amount': str(transaction_data['amount']),
            'merchant_name': transaction_data['merchant_name'],
            'transaction_date': transaction_data['transaction_date'].isoformat(),
            'status': transaction_data.get('transaction_status', 'UNKNOWN'),
        }
