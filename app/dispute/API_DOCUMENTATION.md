# Disputes API Documentation

## Overview

The Disputes API allows businesses to manage transaction disputes with role-based permissions. Business owners and team members can create, view, and manage disputes based on their assigned roles.

## Base URL
```
/api/v1/disputes/
```

## Authentication
All endpoints require authentication. Include the authorization header:
```
Authorization: Bearer <your_token>
```

## Role-Based Permissions

### Business Roles and Permissions:
- **Business Owner**: Can create and read their own disputes
- **Merchant Admin**: Can create disputes and read all business disputes  
- **Customer Support**: Can only read all business disputes
- **Operations**: Can create disputes and read all business disputes
- **Reconciliation**: No dispute access
- **Developer**: No dispute access

---

## Endpoints

### 1. Create Dispute

**POST** `/api/v1/disputes/`

Create a new dispute for a transaction.

#### Request Body:
```json
{
    "transaction_reference": "TXN_20241218_001234",
    "vas_service": "AIRTIME",
    "amount": "1500.00",
    "charge": "22.50",
    "stamp_duty": "50.00",
    "previous_balance": "5000.00",
    "new_balance": "3427.50",
    "transaction_date": "2024-12-18T10:30:00Z",
    "merchant_name": "Test Business Ltd",
    "message": "Transaction failed but amount was debited from my account. Please investigate and refund."
}
```

#### Response (201 Created):
```json
{
    "success": true,
    "message": "Dispute created successfully",
    "data": {
        "id": "dispute_123456789",
        "transaction_reference": "TXN_20241218_001234",
        "vas_service": "AIRTIME",
        "vas_service_display": "Airtime",
        "amount": "1500.00",
        "charge": "22.50",
        "stamp_duty": "50.00",
        "previous_balance": "5000.00",
        "new_balance": "3427.50",
        "transaction_date": "2024-12-18T10:30:00Z",
        "status": "PENDING",
        "status_display": "Pending",
        "merchant_name": "Test Business Ltd",
        "message": "Transaction failed but amount was debited from my account. Please investigate and refund.",
        "created_by_name": "John Doe",
        "created_by_email": "<EMAIL>",
        "business_name": "Test Business Ltd",
        "resolved_by_name": null,
        "resolved_at": null,
        "resolution_notes": null,
        "created_at": "2024-12-18T11:00:00Z",
        "updated_at": "2024-12-18T11:00:00Z"
    }
}
```

---

### 2. List Disputes

**GET** `/api/v1/disputes/`

Retrieve a paginated list of disputes for the user's business.

#### Query Parameters:
- `page` (int): Page number (default: 1)
- `page_size` (int): Items per page (default: 20)
- `status` (string): Filter by status (PENDING, IN_REVIEW, RESOLVED)
- `vas_service` (string): Filter by VAS service type
- `transaction_reference` (string): Filter by transaction reference (partial match)
- `merchant_name` (string): Filter by merchant name (partial match)
- `amount_min` (decimal): Filter by minimum amount
- `amount_max` (decimal): Filter by maximum amount
- `created_at_after` (datetime): Filter disputes created after this date
- `created_at_before` (datetime): Filter disputes created before this date
- `search` (string): Search in transaction reference, merchant name, or message

#### Example Request:
```
GET /api/v1/disputes/?status=PENDING&page=1&page_size=10
```

#### Response (200 OK):
```json
{
    "success": true,
    "message": "Disputes retrieved successfully",
    "data": {
        "count": 25,
        "next": "http://localhost:8000/api/v1/disputes/?page=2",
        "previous": null,
        "results": [
            {
                "id": "dispute_123456789",
                "transaction_reference": "TXN_20241218_001234",
                "vas_service": "AIRTIME",
                "vas_service_display": "Airtime",
                "amount": "1500.00",
                "status": "PENDING",
                "status_display": "Pending",
                "merchant_name": "Test Business Ltd",
                "created_by_name": "John Doe",
                "business_name": "Test Business Ltd",
                "created_at": "2024-12-18T11:00:00Z",
                "updated_at": "2024-12-18T11:00:00Z"
            }
        ]
    }
}
```

---

### 3. Get Dispute Details

**GET** `/api/v1/disputes/{dispute_id}/`

Retrieve detailed information about a specific dispute.

#### Response (200 OK):
```json
{
    "success": true,
    "message": "Dispute retrieved successfully",
    "data": {
        "id": "dispute_123456789",
        "transaction_reference": "TXN_20241218_001234",
        "vas_service": "AIRTIME",
        "vas_service_display": "Airtime",
        "amount": "1500.00",
        "charge": "22.50",
        "stamp_duty": "50.00",
        "previous_balance": "5000.00",
        "new_balance": "3427.50",
        "transaction_date": "2024-12-18T10:30:00Z",
        "status": "PENDING",
        "status_display": "Pending",
        "merchant_name": "Test Business Ltd",
        "message": "Transaction failed but amount was debited from my account. Please investigate and refund.",
        "created_by_name": "John Doe",
        "created_by_email": "<EMAIL>",
        "business_name": "Test Business Ltd",
        "resolved_by_name": null,
        "resolved_at": null,
        "resolution_notes": null,
        "created_at": "2024-12-18T11:00:00Z",
        "updated_at": "2024-12-18T11:00:00Z"
    }
}
```

---

### 4. Update Dispute Status

**PATCH** `/api/v1/disputes/{dispute_id}/update-status/`

Update the status of a dispute. Only users with appropriate permissions can perform this action.

#### Request Body:
```json
{
    "status": "IN_REVIEW",
    "resolution_notes": "Dispute is now under investigation by our technical team."
}
```

#### Response (200 OK):
```json
{
    "success": true,
    "message": "Dispute status updated successfully",
    "data": {
        "id": "dispute_123456789",
        "transaction_reference": "TXN_20241218_001234",
        "vas_service": "AIRTIME",
        "vas_service_display": "Airtime",
        "amount": "1500.00",
        "charge": "22.50",
        "stamp_duty": "50.00",
        "previous_balance": "5000.00",
        "new_balance": "3427.50",
        "transaction_date": "2024-12-18T10:30:00Z",
        "status": "IN_REVIEW",
        "status_display": "In Review",
        "merchant_name": "Test Business Ltd",
        "message": "Transaction failed but amount was debited from my account. Please investigate and refund.",
        "created_by_name": "John Doe",
        "created_by_email": "<EMAIL>",
        "business_name": "Test Business Ltd",
        "resolved_by_name": null,
        "resolved_at": null,
        "resolution_notes": "Dispute is now under investigation by our technical team.",
        "created_at": "2024-12-18T11:00:00Z",
        "updated_at": "2024-12-18T11:15:00Z"
    }
}
```

---

### 5. Get Dispute Statistics

**GET** `/api/v1/disputes/statistics/`

Get dispute statistics for the user's business.

#### Response (200 OK):
```json
{
    "success": true,
    "message": "Dispute statistics retrieved successfully",
    "data": {
        "total_disputes": 45,
        "pending_disputes": 12,
        "in_review_disputes": 8,
        "resolved_disputes": 25,
        "business_name": "Test Business Ltd",
        "business_id": "business_987654321"
    }
}
```

---

### 6. List Business Team Members

**GET** `/api/v1/disputes/team-members/`

Get team members for the user's business.

#### Response (200 OK):
```json
{
    "success": true,
    "message": "Team members retrieved successfully",
    "data": [
        {
            "id": "member_123456789",
            "user_email": "<EMAIL>",
            "user_name": "Jane Smith",
            "role": "MERCHANT_ADMIN",
            "is_active": true,
            "added_by_name": "John Doe",
            "created_at": "2024-12-15T09:00:00Z",
            "updated_at": "2024-12-15T09:00:00Z"
        }
    ]
}
```

---

## Error Responses

### 400 Bad Request
```json
{
    "success": false,
    "message": "Validation error",
    "errors": {
        "amount": ["Amount must be greater than zero."],
        "vas_service": ["Invalid VAS service. Must be one of: AIRTIME, DATA, ELECTRICITY..."]
    }
}
```

### 401 Unauthorized
```json
{
    "success": false,
    "message": "Authentication credentials were not provided."
}
```

### 403 Forbidden
```json
{
    "success": false,
    "message": "You do not have permission to perform this action."
}
```

### 404 Not Found
```json
{
    "success": false,
    "message": "Dispute not found."
}
```

---

## VAS Service Types

Available VAS service types for disputes:
- `VIRTUAL_ACCOUNT`
- `TRANSFER`
- `AIRTIME`
- `DATA`
- `BETTING`
- `ELECTRICITY`
- `CABLE_TV`
- `SME_DATA`
- `KYC`
- `EDUCATION`
- `EPIN`
- `RECURRING_DEBIT`

---

## Status Transitions

Valid status transitions:
- `PENDING` → `IN_REVIEW`
- `IN_REVIEW` → `RESOLVED` or `PENDING`
- `RESOLVED` → (No further transitions allowed)

---

## Testing

Use the management command to seed test data:

```bash
python manage.py seed_dispute_data --businesses 3 --team-members 5 --disputes 20
```

This will create test businesses, team members, and disputes for testing the API.
