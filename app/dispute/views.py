from django.db.models import Count, Q
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from .enums import DisputeStatus
from .filters import <PERSON>spute<PERSON>ilter
from .models import BusinessMember, Dispute
from .permissions import (
    CanCreateDispute,
    CanReadAllBusinessDisputes,
    CanReadDispute,
    get_user_business_context,
)
from .serializers import (
    BusinessMemberSerializer,
    DisputeCreateSerializer,
    DisputeDetailSerializer,
    DisputeListSerializer,
    DisputeStatsSerializer,
    DisputeUpdateStatusSerializer,
)


class BusinessMemberViewSet(viewsets.ModelViewSet):
    """ViewSet for managing business team members."""

    serializer_class = BusinessMemberSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ['user__email', 'user__firstname', 'user__lastname', 'role']
    filterset_fields = ['role', 'is_active']

    def get_queryset(self):
        """Get team members for the user's business."""
        business, _ = get_user_business_context(self.request.user)
        if not business:
            return BusinessMember.objects.none()

        return BusinessMember.objects.filter(business=business).select_related(
            'user', 'added_by', 'business'
        )


class DisputeViewSet(viewsets.ModelViewSet):
    """ViewSet for managing disputes."""

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = DisputeFilter
    search_fields = ['transaction_reference', 'merchant_name', 'message']
    ordering_fields = ['created_at', 'amount', 'transaction_date', 'status']
    ordering = ['-created_at']

    def get_queryset(self):
        """Get disputes based on user's business and permissions."""
        business, role = get_user_business_context(self.request.user)
        if not business:
            return Dispute.objects.none()

        # Base queryset for the business
        queryset = Dispute.objects.filter(business=business).select_related(
            'business', 'created_by', 'resolved_by'
        )

        return queryset

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return DisputeCreateSerializer
        elif self.action in ['list']:
            return DisputeListSerializer
        elif self.action == 'update_status':
            return DisputeUpdateStatusSerializer
        else:
            return DisputeDetailSerializer

    def get_permissions(self):
        """Return appropriate permissions based on action."""
        if self.action == 'create':
            permission_classes = [IsAuthenticated, CanCreateDispute]
        elif self.action in ['list', 'retrieve']:
            # For list/retrieve, we'll handle permissions in get_queryset
            permission_classes = [IsAuthenticated]
        elif self.action in ['update', 'partial_update', 'update_status']:
            # Only certain roles can update disputes (typically admin roles)
            permission_classes = [IsAuthenticated, CanReadAllBusinessDisputes]
        else:
            permission_classes = [IsAuthenticated]

        return [permission() for permission in permission_classes]

    def create(self, request, *args, **kwargs):
        """Create a new dispute."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        dispute = serializer.save()

        response_serializer = DisputeDetailSerializer(dispute)
        return Response(
            {
                "success": True,
                "message": "Dispute created successfully",
                "data": response_serializer.data
            },
            status=status.HTTP_201_CREATED
        )

    def list(self, request, *args, **kwargs):
        """List disputes with pagination."""
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response({
                "success": True,
                "message": "Disputes retrieved successfully",
                "data": serializer.data
            })

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            "success": True,
            "message": "Disputes retrieved successfully",
            "data": serializer.data
        })

    def retrieve(self, request, *args, **kwargs):
        """Retrieve a specific dispute."""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            "success": True,
            "message": "Dispute retrieved successfully",
            "data": serializer.data
        })

    @action(detail=True, methods=['patch'], url_path='update-status')
    def update_status(self, request, pk=None):
        """Update dispute status."""
        dispute = self.get_object()
        serializer = DisputeUpdateStatusSerializer(
            dispute,
            data=request.data,
            partial=True,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        updated_dispute = serializer.save()

        response_serializer = DisputeDetailSerializer(updated_dispute)
        return Response({
            "success": True,
            "message": "Dispute status updated successfully",
            "data": response_serializer.data
        })

    @action(detail=False, methods=['get'], url_path='statistics')
    def statistics(self, request):
        """Get dispute statistics for the business."""
        business, _ = get_user_business_context(request.user)
        if not business:
            return Response({
                "success": False,
                "message": "User is not associated with any business",
                "data": None
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get dispute counts by status
        stats = Dispute.objects.filter(business=business).aggregate(
            total_disputes=Count('id'),
            pending_disputes=Count('id', filter=Q(status=DisputeStatus.PENDING.value)),
            in_review_disputes=Count('id', filter=Q(status=DisputeStatus.IN_REVIEW.value)),
            resolved_disputes=Count('id', filter=Q(status=DisputeStatus.RESOLVED.value)),
        )

        stats['business_name'] = business.name or "Unnamed Business"
        stats['business_id'] = str(business.id)

        serializer = DisputeStatsSerializer(stats)
        return Response({
            "success": True,
            "message": "Dispute statistics retrieved successfully",
            "data": serializer.data
        })
