import random
from decimal import Decimal
from datetime import datetime, timedelta

from business.models import Business
from django.core.management.base import BaseCommand
from django.utils import timezone
from transaction.enums import TransactionClassEnum
from user.models import User

from dispute.enums import BusinessRole, DisputeStatus
from dispute.models import BusinessMember, Dispute


class Command(BaseCommand):
    help = 'Seed database with test data for disputes feature'

    def add_arguments(self, parser):
        parser.add_argument(
            '--businesses',
            type=int,
            default=3,
            help='Number of businesses to create (default: 3)'
        )
        parser.add_argument(
            '--team-members',
            type=int,
            default=5,
            help='Number of team members per business (default: 5)'
        )
        parser.add_argument(
            '--disputes',
            type=int,
            default=20,
            help='Number of disputes per business (default: 20)'
        )
        parser.add_argument(
            '--clean',
            action='store_true',
            help='Clean existing test data before seeding'
        )

    def handle(self, *args, **options):
        if options['clean']:
            self.stdout.write(self.style.WARNING('Cleaning existing test data...'))
            self.clean_test_data()

        businesses_count = options['businesses']
        team_members_count = options['team_members']
        disputes_count = options['disputes']

        self.stdout.write(self.style.SUCCESS('Starting data seeding...'))

        # Create businesses with owners
        businesses = self.create_businesses(businesses_count)
        self.stdout.write(
            self.style.SUCCESS(f'Created {len(businesses)} businesses')
        )

        # Create team members for each business
        total_members = 0
        for business in businesses:
            members = self.create_team_members(business, team_members_count)
            total_members += len(members)
        
        self.stdout.write(
            self.style.SUCCESS(f'Created {total_members} team members')
        )

        # Create disputes for each business
        total_disputes = 0
        for business in businesses:
            disputes = self.create_disputes(business, disputes_count)
            total_disputes += len(disputes)
        
        self.stdout.write(
            self.style.SUCCESS(f'Created {total_disputes} disputes')
        )

        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ Data seeding completed successfully!\n'
                f'📊 Summary:\n'
                f'   - Businesses: {len(businesses)}\n'
                f'   - Team Members: {total_members}\n'
                f'   - Disputes: {total_disputes}\n'
            )
        )

        # Print sample login credentials
        self.print_sample_credentials(businesses)

    def clean_test_data(self):
        """Clean existing test data."""
        # Delete disputes first (due to foreign key constraints)
        Dispute.objects.filter(
            transaction_reference__startswith='TEST_'
        ).delete()
        
        # Delete business members
        BusinessMember.objects.filter(
            user__email__contains='testuser'
        ).delete()
        
        # Delete test businesses and users
        Business.objects.filter(
            email__contains='testbusiness'
        ).delete()
        
        User.objects.filter(
            email__contains='testuser'
        ).delete()

    def create_businesses(self, count):
        """Create test businesses with owners."""
        businesses = []
        
        for i in range(1, count + 1):
            # Create business owner
            owner = User.objects.create_user(
                email=f'testuser.owner{i}@example.com',
                password='testpass123',
                firstname=f'Business',
                lastname=f'Owner{i}',
                role='Business_Owner',
                verified=True,
                is_active=True
            )
            
            # Create business
            business = Business.objects.create(
                name=f'Test Business {i}',
                owner=owner,
                email=f'testbusiness{i}@example.com',
                description=f'Test business {i} for dispute testing',
                phone=f'+234801234567{i}',
                status='Active',
                office_address=f'{i} Test Street',
                city='Lagos',
                state='Lagos',
                postal_code='100001'
            )
            
            businesses.append(business)
            
        return businesses

    def create_team_members(self, business, count):
        """Create team members for a business."""
        members = []
        roles = [
            BusinessRole.MERCHANT_ADMIN,
            BusinessRole.CUSTOMER_SUPPORT,
            BusinessRole.OPERATIONS,
            BusinessRole.RECONCILIATION,
            BusinessRole.DEVELOPER
        ]
        
        business_id = business.id[-3:]  # Last 3 chars of business ID
        
        for i in range(1, count + 1):
            role = roles[(i - 1) % len(roles)]
            
            # Map business role to user role
            user_role_mapping = {
                BusinessRole.MERCHANT_ADMIN: 'Merchant_Admin',
                BusinessRole.CUSTOMER_SUPPORT: 'Customer_Support',
                BusinessRole.OPERATIONS: 'Operations',
                BusinessRole.RECONCILIATION: 'Reconciliation',
                BusinessRole.DEVELOPER: 'Developer'
            }

            # Create user
            user = User.objects.create_user(
                email=f'testuser.{role.value.lower()}{i}.{business_id}@example.com',
                password='testpass123',
                firstname=f'{role.value.replace("_", " ").title()}',
                lastname=f'User{i}',
                role=user_role_mapping.get(role, 'Business_Owner'),
                verified=True,
                is_active=True
            )
            
            # Create business member
            member = BusinessMember.objects.create(
                business=business,
                user=user,
                role=role.value,
                is_active=True,
                added_by=business.owner
            )
            
            members.append(member)
            
        return members

    def create_disputes(self, business, count):
        """Create test disputes for a business."""
        disputes = []
        vas_services = [choice[0] for choice in TransactionClassEnum.choices()]
        statuses = [choice[0] for choice in DisputeStatus.choices()]
        
        # Get potential dispute creators (business owner + team members who can create)
        creators = [business.owner]
        team_members = BusinessMember.objects.filter(
            business=business,
            role__in=[
                BusinessRole.MERCHANT_ADMIN.value,
                BusinessRole.OPERATIONS.value
            ]
        )
        creators.extend([member.user for member in team_members])
        
        business_id = business.id[-3:]  # Last 3 chars of business ID
        
        for i in range(1, count + 1):
            # Random transaction details
            amount = Decimal(str(random.uniform(100, 10000))).quantize(Decimal('0.01'))
            charge = amount * Decimal('0.015')  # 1.5% charge
            stamp_duty = Decimal('50.00') if amount > 1000 else Decimal('0.00')
            previous_balance = Decimal(str(random.uniform(5000, 50000))).quantize(Decimal('0.01'))
            new_balance = previous_balance - amount - charge - stamp_duty
            
            # Random dates within last 30 days
            transaction_date = timezone.now() - timedelta(
                days=random.randint(1, 30),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59)
            )
            
            dispute = Dispute.objects.create(
                business=business,
                created_by=random.choice(creators),
                transaction_reference=f'TEST_TXN_{business_id}_{i:04d}',
                vas_service=random.choice(vas_services),
                amount=amount,
                charge=charge,
                stamp_duty=stamp_duty,
                previous_balance=previous_balance,
                new_balance=new_balance,
                transaction_date=transaction_date,
                status=random.choice(statuses),
                merchant_name=business.name or f'Test Merchant {business_id}',
                message=self.generate_dispute_message(i)
            )
            
            # If dispute is resolved, add resolution details
            if dispute.status == DisputeStatus.RESOLVED.value:
                dispute.resolved_by = business.owner
                dispute.resolved_at = timezone.now() - timedelta(
                    days=random.randint(0, 5)
                )
                dispute.resolution_notes = f'Dispute resolved after investigation. Issue was {random.choice(["refunded", "corrected", "explained to customer"])}.'
                dispute.save()
            
            disputes.append(dispute)
            
        return disputes

    def generate_dispute_message(self, index):
        """Generate realistic dispute messages."""
        messages = [
            f"Transaction failed but amount was debited from my account. Please investigate and refund.",
            f"I was charged twice for the same transaction. The second charge should be reversed.",
            f"The transaction was successful but the service was not delivered. Need immediate resolution.",
            f"Wrong amount was charged. I was supposed to pay less but more was debited.",
            f"Transaction shows as failed on my end but recipient confirms they received the service.",
            f"Duplicate transaction - same service was charged multiple times in error.",
            f"Service delivery failed but payment was processed. Please check and resolve.",
            f"Incorrect charges applied to my transaction. The fee structure seems wrong.",
            f"Transaction timeout occurred but amount was still debited from wallet.",
            f"Partial service delivery - paid for full service but only received partial."
        ]
        
        return messages[(index - 1) % len(messages)]

    def print_sample_credentials(self, businesses):
        """Print sample login credentials for testing."""
        self.stdout.write(
            self.style.SUCCESS(
                f'\n🔑 Sample Login Credentials:\n'
                f'{"="*50}\n'
            )
        )
        
        for i, business in enumerate(businesses, 1):
            self.stdout.write(
                f'Business {i}: {business.name}\n'
                f'  Owner: {business.owner.email} / testpass123\n'
            )
            
            # Show first team member of each role
            team_members = BusinessMember.objects.filter(business=business)[:3]
            for member in team_members:
                self.stdout.write(
                    f'  {member.role}: {member.user.email} / testpass123\n'
                )
            self.stdout.write('')
