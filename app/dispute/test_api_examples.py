#!/usr/bin/env python3
"""
API Testing Examples for Disputes Feature

This script demonstrates how to use the Disputes API with sample requests and responses.
Run this script to test the API endpoints after seeding test data.

Usage:
    python test_api_examples.py

Make sure to:
1. Run the seed command first: python manage.py seed_dispute_data --clean --businesses 3 --team-members 5 --disputes 15
2. Have the Django server running on localhost:47001
"""

import json
import requests
from datetime import datetime, timedelta


class DisputeAPITester:
    def __init__(self, base_url="http://localhost:47001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token = None
        
    def login(self, email, password):
        """Login and get authentication token."""
        login_url = f"{self.base_url}/api/v1/auth/login/"
        data = {
            "email": email,
            "password": password
        }
        
        print(f"🔐 Logging in as {email}...")
        response = self.session.post(login_url, json=data)
        
        if response.status_code == 200:
            result = response.json()
            self.auth_token = result.get('data', {}).get('access_token')
            self.session.headers.update({
                'Authorization': f'Bearer {self.auth_token}'
            })
            print(f"✅ Login successful!")
            return True
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(response.text)
            return False
    
    def create_dispute(self):
        """Test creating a new dispute."""
        url = f"{self.base_url}/api/v1/disputes/"
        
        # Sample dispute data
        dispute_data = {
            "transaction_reference": f"TEST_API_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "vas_service": "AIRTIME",
            "amount": "1500.00",
            "charge": "22.50",
            "stamp_duty": "50.00",
            "previous_balance": "5000.00",
            "new_balance": "3427.50",
            "transaction_date": (datetime.now() - timedelta(hours=2)).isoformat() + "Z",
            "merchant_name": "Test Business API",
            "message": "Transaction failed but amount was debited from my account. Please investigate and refund."
        }
        
        print("\n📝 Creating a new dispute...")
        print("Request Data:")
        print(json.dumps(dispute_data, indent=2))
        
        response = self.session.post(url, json=dispute_data)
        
        print(f"\nResponse Status: {response.status_code}")
        print("Response Data:")
        print(json.dumps(response.json(), indent=2))
        
        if response.status_code == 201:
            dispute_id = response.json()['data']['id']
            print(f"✅ Dispute created successfully with ID: {dispute_id}")
            return dispute_id
        else:
            print("❌ Failed to create dispute")
            return None
    
    def list_disputes(self):
        """Test listing disputes with filters."""
        url = f"{self.base_url}/api/v1/disputes/"
        
        # Test with filters
        params = {
            "status": "PENDING",
            "page": 1,
            "page_size": 5
        }
        
        print("\n📋 Listing disputes with filters...")
        print(f"Filters: {params}")
        
        response = self.session.get(url, params=params)
        
        print(f"\nResponse Status: {response.status_code}")
        print("Response Data:")
        print(json.dumps(response.json(), indent=2))
        
        if response.status_code == 200:
            disputes = response.json()['data']['results']
            print(f"✅ Retrieved {len(disputes)} disputes")
            return disputes
        else:
            print("❌ Failed to list disputes")
            return []
    
    def get_dispute_details(self, dispute_id):
        """Test getting dispute details."""
        url = f"{self.base_url}/api/v1/disputes/{dispute_id}/"
        
        print(f"\n🔍 Getting details for dispute {dispute_id}...")
        
        response = self.session.get(url)
        
        print(f"\nResponse Status: {response.status_code}")
        print("Response Data:")
        print(json.dumps(response.json(), indent=2))
        
        if response.status_code == 200:
            print("✅ Dispute details retrieved successfully")
            return response.json()['data']
        else:
            print("❌ Failed to get dispute details")
            return None
    
    def update_dispute_status(self, dispute_id):
        """Test updating dispute status."""
        url = f"{self.base_url}/api/v1/disputes/{dispute_id}/update-status/"
        
        update_data = {
            "status": "IN_REVIEW",
            "resolution_notes": "Dispute is now under investigation by our technical team."
        }
        
        print(f"\n🔄 Updating status for dispute {dispute_id}...")
        print("Update Data:")
        print(json.dumps(update_data, indent=2))
        
        response = self.session.patch(url, json=update_data)
        
        print(f"\nResponse Status: {response.status_code}")
        print("Response Data:")
        print(json.dumps(response.json(), indent=2))
        
        if response.status_code == 200:
            print("✅ Dispute status updated successfully")
            return True
        else:
            print("❌ Failed to update dispute status")
            return False
    
    def get_dispute_statistics(self):
        """Test getting dispute statistics."""
        url = f"{self.base_url}/api/v1/disputes/statistics/"
        
        print("\n📊 Getting dispute statistics...")
        
        response = self.session.get(url)
        
        print(f"\nResponse Status: {response.status_code}")
        print("Response Data:")
        print(json.dumps(response.json(), indent=2))
        
        if response.status_code == 200:
            print("✅ Statistics retrieved successfully")
            return response.json()['data']
        else:
            print("❌ Failed to get statistics")
            return None
    
    def list_team_members(self):
        """Test listing business team members."""
        url = f"{self.base_url}/api/v1/disputes/team-members/"
        
        print("\n👥 Listing business team members...")
        
        response = self.session.get(url)
        
        print(f"\nResponse Status: {response.status_code}")
        print("Response Data:")
        print(json.dumps(response.json(), indent=2))
        
        if response.status_code == 200:
            members = response.json()['data']
            print(f"✅ Retrieved {len(members)} team members")
            return members
        else:
            print("❌ Failed to list team members")
            return []
    
    def run_full_test(self):
        """Run a complete test of all API endpoints."""
        print("🚀 Starting Disputes API Test Suite")
        print("=" * 50)
        
        # Test with business owner credentials
        if not self.login("<EMAIL>", "testpass123"):
            return
        
        # Test all endpoints
        dispute_id = self.create_dispute()
        
        disputes = self.list_disputes()
        
        if dispute_id:
            self.get_dispute_details(dispute_id)
            self.update_dispute_status(dispute_id)
        elif disputes:
            # Use existing dispute if creation failed
            dispute_id = disputes[0]['id']
            self.get_dispute_details(dispute_id)
        
        self.get_dispute_statistics()
        self.list_team_members()
        
        print("\n" + "=" * 50)
        print("🎉 API Test Suite Completed!")


def main():
    """Main function to run the API tests."""
    tester = DisputeAPITester()
    tester.run_full_test()


if __name__ == "__main__":
    main()
