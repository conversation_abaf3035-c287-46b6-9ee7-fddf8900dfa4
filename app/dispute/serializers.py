from decimal import Decimal

from django.utils import timezone
from rest_framework import serializers
from transaction.enums import TransactionClassEnum

from .enums import DisputeStatus
from .models import BusinessMember, Dispute
from .permissions import get_user_business_context


class BusinessMemberSerializer(serializers.ModelSerializer):
    """Serializer for business team members."""
    
    user_email = serializers.EmailField(source='user.email', read_only=True)
    user_name = serializers.CharField(source='user.fullname', read_only=True)
    added_by_name = serializers.CharField(source='added_by.fullname', read_only=True)

    class Meta:
        model = BusinessMember
        fields = [
            'id', 'user_email', 'user_name', 'role', 'is_active',
            'added_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class DisputeCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating disputes."""
    
    class Meta:
        model = Dispute
        fields = [
            'transaction_reference', 'vas_service', 'amount', 'charge',
            'stamp_duty', 'previous_balance', 'new_balance', 'transaction_date',
            'merchant_name', 'message'
        ]

    def validate_vas_service(self, value):
        """Validate VAS service type."""
        valid_services = [choice[0] for choice in TransactionClassEnum.choices()]
        if value not in valid_services:
            raise serializers.ValidationError(
                f"Invalid VAS service. Must be one of: {', '.join(valid_services)}"
            )
        return value

    def validate_amount(self, value):
        """Validate transaction amount."""
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than zero.")
        return value

    def validate(self, attrs):
        """Validate the entire dispute data."""
        # Ensure new_balance calculation is consistent
        expected_new_balance = attrs['previous_balance'] - attrs['amount'] - attrs['charge'] - attrs['stamp_duty']
        
        if abs(attrs['new_balance'] - expected_new_balance) > Decimal('0.01'):
            raise serializers.ValidationError(
                "New balance calculation doesn't match the provided values."
            )
        
        return attrs

    def create(self, validated_data):
        """Create a new dispute."""
        request = self.context.get('request')
        business, role = get_user_business_context(request.user)
        
        if not business:
            raise serializers.ValidationError("User is not associated with any business.")
        
        validated_data['business'] = business
        validated_data['created_by'] = request.user
        validated_data['status'] = DisputeStatus.PENDING.value
        
        return super().create(validated_data)


class DisputeListSerializer(serializers.ModelSerializer):
    """Serializer for listing disputes."""
    
    created_by_name = serializers.CharField(source='created_by.fullname', read_only=True)
    business_name = serializers.CharField(source='business.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    vas_service_display = serializers.CharField(source='get_vas_service_display', read_only=True)

    class Meta:
        model = Dispute
        fields = [
            'id', 'transaction_reference', 'vas_service', 'vas_service_display',
            'amount', 'status', 'status_display', 'merchant_name',
            'created_by_name', 'business_name', 'created_at', 'updated_at'
        ]


class DisputeDetailSerializer(serializers.ModelSerializer):
    """Serializer for dispute details."""
    
    created_by_name = serializers.CharField(source='created_by.fullname', read_only=True)
    created_by_email = serializers.EmailField(source='created_by.email', read_only=True)
    business_name = serializers.CharField(source='business.name', read_only=True)
    resolved_by_name = serializers.CharField(source='resolved_by.fullname', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    vas_service_display = serializers.CharField(source='get_vas_service_display', read_only=True)

    class Meta:
        model = Dispute
        fields = [
            'id', 'transaction_reference', 'vas_service', 'vas_service_display',
            'amount', 'charge', 'stamp_duty', 'previous_balance', 'new_balance',
            'transaction_date', 'status', 'status_display', 'merchant_name',
            'message', 'created_by_name', 'created_by_email', 'business_name',
            'resolved_by_name', 'resolved_at', 'resolution_notes',
            'created_at', 'updated_at'
        ]


class DisputeUpdateStatusSerializer(serializers.ModelSerializer):
    """Serializer for updating dispute status."""
    
    class Meta:
        model = Dispute
        fields = ['status', 'resolution_notes']

    def validate_status(self, value):
        """Validate status transition."""
        if self.instance:
            current_status = self.instance.status
            
            # Define valid status transitions
            valid_transitions = {
                DisputeStatus.PENDING.value: [DisputeStatus.IN_REVIEW.value],
                DisputeStatus.IN_REVIEW.value: [DisputeStatus.RESOLVED.value, DisputeStatus.PENDING.value],
                DisputeStatus.RESOLVED.value: []  # No transitions from resolved
            }
            
            if value not in valid_transitions.get(current_status, []):
                raise serializers.ValidationError(
                    f"Cannot transition from {current_status} to {value}"
                )
        
        return value

    def update(self, instance, validated_data):
        """Update dispute status."""
        if 'status' in validated_data and validated_data['status'] == DisputeStatus.RESOLVED.value:
            validated_data['resolved_by'] = self.context['request'].user
            validated_data['resolved_at'] = timezone.now()
        
        return super().update(instance, validated_data)


class DisputeStatsSerializer(serializers.Serializer):
    """Serializer for dispute statistics."""
    
    total_disputes = serializers.IntegerField()
    pending_disputes = serializers.IntegerField()
    in_review_disputes = serializers.IntegerField()
    resolved_disputes = serializers.IntegerField()
    business_name = serializers.CharField()
    business_id = serializers.CharField()
