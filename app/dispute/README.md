# Disputes Feature Implementation

## Overview

This implementation provides a comprehensive disputes management system for the SageCloud Core API. The feature allows businesses to manage transaction disputes with role-based permissions and team management capabilities.

## Features Implemented

### ✅ Core Models
- **BusinessMember**: Manages business team members with specific roles
- **Dispute**: Handles transaction disputes with full transaction details

### ✅ Role-Based Permissions
- **Business Owner**: Can create and read their own disputes
- **Merchant Admin**: Can create disputes and read all business disputes
- **Customer Support**: Can only read all business disputes
- **Operations**: Can create disputes and read all business disputes
- **Reconciliation**: No dispute access
- **Developer**: No dispute access

### ✅ API Endpoints
- `POST /api/v1/disputes/` - Create new dispute
- `GET /api/v1/disputes/` - List disputes with filtering and pagination
- `GET /api/v1/disputes/{id}/` - Get dispute details
- `PATCH /api/v1/disputes/{id}/update-status/` - Update dispute status
- `GET /api/v1/disputes/statistics/` - Get dispute statistics
- `GET /api/v1/disputes/team-members/` - List business team members

### ✅ Advanced Features
- Comprehensive filtering and search
- Status transition validation
- Audit trail with timestamps
- Pagination support
- Role-based access control
- Business context isolation

## Quick Start

### 1. Database Setup
```bash
# Create and run migrations
docker compose exec api python manage.py makemigrations dispute
docker compose exec api python manage.py migrate
```

### 2. Seed Test Data
```bash
# Create test businesses, team members, and disputes
docker compose exec api python manage.py seed_dispute_data --clean --businesses 3 --team-members 5 --disputes 15
```

### 3. Test the API
```bash
# Run the API test examples
docker compose exec api python dispute/test_api_examples.py
```

## File Structure

```
app/dispute/
├── __init__.py
├── admin.py                    # Django admin configuration
├── apps.py                     # App configuration
├── enums.py                    # Enums for status, roles, permissions
├── models.py                   # BusinessMember and Dispute models
├── permissions.py              # Role-based permission classes
├── serializers.py              # API serializers
├── views.py                    # ViewSets for API endpoints
├── urls.py                     # URL routing
├── filters.py                  # Query filtering
├── migrations/                 # Database migrations
├── management/
│   └── commands/
│       └── seed_dispute_data.py # Test data seeding command
├── API_DOCUMENTATION.md        # Comprehensive API docs
├── README.md                   # This file
└── test_api_examples.py        # API testing script
```

## Sample Login Credentials

After running the seed command, you can use these credentials:

### Business 1: Test Business 1
- **Owner**: `<EMAIL>` / `testpass123`
- **Operations**: `<EMAIL>` / `testpass123`
- **Customer Support**: `<EMAIL>` / `testpass123`

### Business 2: Test Business 2
- **Owner**: `<EMAIL>` / `testpass123`
- **Merchant Admin**: `<EMAIL>` / `testpass123`

## API Usage Examples

### Create a Dispute
```bash
curl -X POST http://localhost:47001/api/v1/disputes/ \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_reference": "TXN_20241218_001234",
    "vas_service": "AIRTIME",
    "amount": "1500.00",
    "charge": "22.50",
    "stamp_duty": "50.00",
    "previous_balance": "5000.00",
    "new_balance": "3427.50",
    "transaction_date": "2024-12-18T10:30:00Z",
    "merchant_name": "Test Business Ltd",
    "message": "Transaction failed but amount was debited."
  }'
```

### List Disputes with Filters
```bash
curl -X GET "http://localhost:47001/api/v1/disputes/?status=PENDING&page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Get Dispute Statistics
```bash
curl -X GET http://localhost:47001/api/v1/disputes/statistics/ \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Database Schema

### BusinessMember Model
- Links users to businesses with specific roles
- Tracks who added the member and when
- Supports active/inactive status

### Dispute Model
- Complete transaction details (reference, amount, charges, balances)
- VAS service type and merchant information
- Status tracking with resolution details
- Audit trail with created/updated timestamps

## Permission System

The permission system is built around business roles:

1. **Role Assignment**: Users are assigned roles within specific businesses
2. **Permission Mapping**: Each role has specific permissions (create, read, etc.)
3. **Business Isolation**: Users can only access disputes from their business
4. **Granular Control**: Different roles have different levels of access

## Status Management

Disputes follow a controlled status flow:
- **PENDING** → **IN_REVIEW**
- **IN_REVIEW** → **RESOLVED** or back to **PENDING**
- **RESOLVED** → No further transitions

## Testing

### Unit Tests
```bash
# Run dispute-specific tests
docker compose exec api python manage.py test dispute
```

### API Testing
```bash
# Run the comprehensive API test script
docker compose exec api python dispute/test_api_examples.py
```

### Manual Testing
1. Use the seeded test data
2. Login with different role credentials
3. Test permission boundaries
4. Verify business isolation

## Admin Interface

The Django admin interface provides:
- Business member management
- Dispute viewing and editing
- Status badges for visual clarity
- Search and filtering capabilities

Access at: `http://localhost:47001/admin/`

## Monitoring and Logging

- All API requests are logged via the existing audit system
- Database queries are optimized with select_related
- Pagination prevents large data loads
- Proper error handling and validation

## Security Considerations

- Role-based access control prevents unauthorized access
- Business isolation ensures data privacy
- Input validation prevents malicious data
- Authentication required for all endpoints

## Future Enhancements

Potential improvements for future versions:
- File attachments for disputes
- Email notifications for status changes
- Dispute escalation workflows
- Integration with external ticketing systems
- Advanced analytics and reporting

## Support

For questions or issues:
1. Check the API documentation in `API_DOCUMENTATION.md`
2. Review the test examples in `test_api_examples.py`
3. Examine the seeded test data structure
4. Use the Django admin interface for debugging
