from django.contrib import admin
from django.utils.html import format_html

from .models import BusinessMember, Dispute


@admin.register(BusinessMember)
class BusinessMemberAdmin(admin.ModelAdmin):
    list_display = [
        'business_name', 'user_email', 'user_name', 'role',
        'is_active', 'added_by_name', 'created_at'
    ]
    list_filter = ['role', 'is_active', 'created_at']
    search_fields = [
        'business__name', 'user__email', 'user__firstname',
        'user__lastname', 'role'
    ]
    readonly_fields = ['created_at', 'updated_at']

    def business_name(self, obj):
        return obj.business.name or "Unnamed Business"
    business_name.short_description = 'Business'

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'User Email'

    def user_name(self, obj):
        return obj.user.fullname
    user_name.short_description = 'User Name'

    def added_by_name(self, obj):
        return obj.added_by.fullname if obj.added_by else '-'
    added_by_name.short_description = 'Added By'


@admin.register(Dispute)
class DisputeAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'transaction_reference', 'business_name', 'vas_service',
        'amount', 'status_badge', 'merchant_name', 'created_by_name', 'created_at'
    ]
    list_filter = ['status', 'vas_service', 'created_at', 'transaction_date']
    search_fields = [
        'transaction_reference', 'business__name', 'merchant_name',
        'created_by__email', 'message'
    ]
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Transaction Details', {
            'fields': (
                'transaction_reference', 'vas_service', 'amount', 'charge',
                'stamp_duty', 'previous_balance', 'new_balance', 'transaction_date'
            )
        }),
        ('Dispute Information', {
            'fields': (
                'business', 'created_by', 'merchant_name', 'message', 'status'
            )
        }),
        ('Resolution', {
            'fields': (
                'resolved_by', 'resolved_at', 'resolution_notes'
            ),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def business_name(self, obj):
        return obj.business.name or "Unnamed Business"
    business_name.short_description = 'Business'

    def created_by_name(self, obj):
        return obj.created_by.fullname
    created_by_name.short_description = 'Created By'

    def status_badge(self, obj):
        colors = {
            'PENDING': '#ffc107',
            'IN_REVIEW': '#17a2b8',
            'RESOLVED': '#28a745'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 11px;">{}</span>',
            color, obj.status.replace('_', ' ')
        )
    status_badge.short_description = 'Status'
