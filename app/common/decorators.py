from functools import wraps

from business.models import Business, OnboardingStage
from rest_framework import status
from rest_framework.response import Response
from transaction.enums import TransactionClassEnum
from transaction.models import Transaction
from vas.permissions import HasActiveVASProductPermission


def merchant_onboarding_required(view_func):
    @wraps(view_func)
    def _wrapped_view(self, request, *args, **kwargs):
        user = request.user
        try:
            business = user.business
        except Business.DoesNotExist:
            return Response(
                {
                    "success": False,
                    "status": "failed",
                    "message": "Authentication Failed.",
                    "data": None,
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        if business.onboarding_stage != OnboardingStage.Completed.value:
            return Response(
                {
                    "success": False,
                    "status": "failed",
                    "message": "Merchant Onboarding Incomplete.",
                    "data": None,
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        request.business = business
        return view_func(self, request, *args, **kwargs)

    return _wrapped_view


def admin_required(view_func):
    @wraps(view_func)
    def _wrapped_view(self, request, *args, **kwargs):
        user = request.user
        if user.role != "Admin":
            return Response(
                {
                    "success": False,
                    "status": "failed",
                    "message": "Access Denied.",
                    "data": None,
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        return view_func(self, request, *args, **kwargs)

    return _wrapped_view


def require_activated_vas_product(product_type: TransactionClassEnum):
    def decorator(view_cls):
        original_permissions = getattr(view_cls, "permission_classes", [])

        class DecoratedView(view_cls):
            required_vas_product = product_type
            permission_classes = original_permissions + [HasActiveVASProductPermission]

        return DecoratedView

    return decorator


def enforce_unique_reference(view_func):
    @wraps(view_func)
    def _wrapped_view(self, request, *args, **kwargs):
        reference = request.data.get("reference", None)

        if not reference:
            return Response(
                {
                    "success": False,
                    "status": "failed",
                    "message": "Reference field is required.",
                    "data": None,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        txn = Transaction.objects.filter(merchant_reference=reference).exists()
        if txn:
            return Response(
                {
                    "success": False,
                    "status": "failed",
                    "message": "Duplicate reference",
                    "data": None,
                },
                status=status.HTTP_409_CONFLICT,
            )

        return view_func(self, request, *args, **kwargs)

    return _wrapped_view
